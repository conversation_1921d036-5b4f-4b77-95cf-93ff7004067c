#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进信号生成系统测试脚本
测试优化后的信号生成逻辑和交易策略
"""

import sys
import os
import traceback
import warnings
import pandas as pd
import numpy as np
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_improved_signal_generation():
    """测试改进的信号生成系统"""
    print("🔧 测试改进的信号生成系统...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=200, freq='D')
        np.random.seed(42)
        
        # 创建更真实的价格数据
        prices = [100]
        for _ in range(199):
            # 添加趋势和噪声
            trend = 0.0005  # 轻微上升趋势
            noise = np.random.normal(0, 0.02)
            change = trend + noise
            prices.append(prices[-1] * (1 + change))
        
        data = pd.DataFrame({
            'dt': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 200)
        })
        
        print(f"   ✅ 测试数据生成完成: {len(data)} 条记录")
        print(f"   📊 价格范围: {min(prices):.2f} - {max(prices):.2f}")
        
        # 测试不同的信号生成配置
        test_configs = [
            {
                'name': '阈值方法 (优化)',
                'config': {
                    'method': 'threshold',
                    'buy_threshold': 0.01,
                    'sell_threshold': -0.01,
                    'signal_smoothing': True,
                    'smoothing_window': 3,
                    'min_holding_period': 2,
                    'require_confirmation': False
                }
            },
            {
                'name': '自适应阈值方法',
                'config': {
                    'method': 'adaptive',
                    'buy_threshold': 0.01,
                    'sell_threshold': -0.01,
                    'lookback_window': 30,
                    'threshold_percentile': 0.7,
                    'signal_smoothing': True,
                    'smoothing_window': 5,
                    'min_holding_period': 3,
                    'require_confirmation': True,
                    'confirmation_window': 2
                }
            },
            {
                'name': '分位数方法',
                'config': {
                    'method': 'quantile',
                    'upper_quantile': 0.75,
                    'lower_quantile': 0.25,
                    'signal_smoothing': True,
                    'smoothing_window': 3,
                    'min_holding_period': 2,
                    'require_confirmation': False
                }
            }
        ]
        
        results = []
        
        for test_config in test_configs:
            print(f"\n   🧪 测试配置: {test_config['name']}")
            
            # 创建ML回测引擎
            ml_config = {
                'model_type': 'random_forest',
                'sequence_length': 20,
                'min_train_samples': 100,
                'feature_engineering': {
                    'technical_indicators': True,
                    'price_features': True,
                    'volume_features': True,
                    'volatility_features': True
                },
                'signal_generation': test_config['config']
            }
            
            ml_engine = MLEnhancedBacktestEngine(
                initial_capital=100000,
                ml_config=ml_config
            )
            
            # 运行回测
            backtest_results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)
            
            # 提取关键指标
            config_results = {
                'config_name': test_config['name'],
                'total_return': backtest_results.get('total_return', 0),
                'trade_count': backtest_results.get('trade_count', 0),
                'win_rate': backtest_results.get('win_rate', 0),
                'sharpe_ratio': backtest_results.get('sharpe_ratio', 0),
                'max_drawdown': backtest_results.get('max_drawdown', 0),
                'ml_metrics': backtest_results.get('ml_metrics', {})
            }
            
            results.append(config_results)
            
            # 打印结果
            print(f"      📈 总收益率: {config_results['total_return']:.4f}")
            print(f"      🔄 交易次数: {config_results['trade_count']}")
            print(f"      🎯 胜率: {config_results['win_rate']:.2%}")
            print(f"      📊 夏普比率: {config_results['sharpe_ratio']:.4f}")
            print(f"      📉 最大回撤: {config_results['max_drawdown']:.4f}")
            
            if 'signal_distribution' in config_results['ml_metrics']:
                signal_dist = config_results['ml_metrics']['signal_distribution']
                print(f"      🚦 信号分布: 买入={signal_dist.get('buy_signals', 0)}, "
                      f"卖出={signal_dist.get('sell_signals', 0)}, "
                      f"持有={signal_dist.get('hold_signals', 0)}")
        
        # 比较结果
        print(f"\n{'='*60}")
        print("📊 信号生成系统测试结果比较")
        print(f"{'='*60}")
        
        best_config = max(results, key=lambda x: x['total_return'])
        most_trades = max(results, key=lambda x: x['trade_count'])
        
        print(f"🏆 最佳收益配置: {best_config['config_name']}")
        print(f"   收益率: {best_config['total_return']:.4f}")
        print(f"   交易次数: {best_config['trade_count']}")
        
        print(f"\n🔄 最多交易配置: {most_trades['config_name']}")
        print(f"   交易次数: {most_trades['trade_count']}")
        print(f"   收益率: {most_trades['total_return']:.4f}")
        
        # 检查是否解决了交易次数为0的问题
        total_trades = sum(r['trade_count'] for r in results)
        if total_trades > 0:
            print(f"\n✅ 成功解决交易次数为0的问题！")
            print(f"   总交易次数: {total_trades}")
            print(f"   平均每个配置: {total_trades/len(results):.1f} 次交易")
        else:
            print(f"\n❌ 仍然存在交易次数为0的问题")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        traceback.print_exc()
        return False


def test_signal_filtering():
    """测试信号过滤功能"""
    print("\n🔍 测试信号过滤功能...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建测试信号序列
        test_signals = pd.Series([0, 1, 1, 1, 0, -1, -1, 0, 1, 0, 1, 1, -1, 0, 0])
        print(f"   原始信号: {test_signals.tolist()}")
        
        ml_engine = MLEnhancedBacktestEngine(initial_capital=100000)
        
        # 测试不同过滤配置
        filter_configs = [
            {
                'name': '无过滤',
                'config': {
                    'signal_smoothing': False,
                    'min_holding_period': 1,
                    'require_confirmation': False
                }
            },
            {
                'name': '信号平滑',
                'config': {
                    'signal_smoothing': True,
                    'smoothing_window': 3,
                    'min_holding_period': 1,
                    'require_confirmation': False
                }
            },
            {
                'name': '最小持仓期',
                'config': {
                    'signal_smoothing': False,
                    'min_holding_period': 3,
                    'require_confirmation': False
                }
            },
            {
                'name': '信号确认',
                'config': {
                    'signal_smoothing': False,
                    'min_holding_period': 1,
                    'require_confirmation': True,
                    'confirmation_window': 2
                }
            }
        ]
        
        for filter_config in filter_configs:
            filtered_signals = ml_engine._apply_signal_filters(test_signals, filter_config['config'])
            print(f"   {filter_config['name']}: {filtered_signals.tolist()}")
        
        print("   ✅ 信号过滤功能测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 信号过滤测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🎯 改进信号生成系统测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("改进信号生成系统", test_improved_signal_generation),
        ("信号过滤功能", test_signal_filtering),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 改进信号生成系统测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed >= total * 0.8:
        print("\n🎉 改进信号生成系统测试基本通过！")
        print("\n💡 改进特性:")
        print("   ✅ 优化的信号阈值")
        print("   ✅ 自适应阈值方法")
        print("   ✅ 信号平滑和过滤")
        print("   ✅ 最小持仓期控制")
        print("   ✅ 信号确认机制")
        print("   ✅ 多种信号生成方法")
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")
    
    return passed >= total * 0.8


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
