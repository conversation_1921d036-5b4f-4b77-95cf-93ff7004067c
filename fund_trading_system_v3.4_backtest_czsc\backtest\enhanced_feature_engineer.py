import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import warnings
from scipy import stats
from scipy.signal import find_peaks
import talib

warnings.filterwarnings('ignore')

# 尝试导入TA-Lib，如果不可用则使用自定义实现
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False


class EnhancedFeatureEngineer:
    """
    增强特征工程模块
    专门为金融时间序列数据设计的特征提取器
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化增强特征工程器
        
        Args:
            config: 特征工程配置
        """
        self.config = config or self._get_default_config()
        self.feature_log = []
        self.feature_importance = {}
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'price_features': {
                'returns': True,
                'log_returns': True,
                'moving_averages': [5, 10, 20, 50],
                'price_ratios': True,
                'price_momentum': True
            },
            'technical_indicators': {
                'rsi': True,
                'macd': True,
                'bollinger_bands': True,
                'stochastic': True,
                'williams_r': True,
                'cci': True,
                'adx': True
            },
            'volume_features': {
                'volume_sma': [5, 20],
                'volume_ratios': True,
                'vwap': True,
                'volume_price_trend': True,
                'on_balance_volume': True
            },
            'volatility_features': {
                'historical_volatility': [5, 10, 20],
                'true_range': True,
                'average_true_range': True,
                'volatility_ratios': True
            },
            'market_microstructure': {
                'bid_ask_spread': True,
                'price_impact': True,
                'market_efficiency': True
            },
            'pattern_features': {
                'support_resistance': True,
                'trend_lines': True,
                'chart_patterns': True
            },
            'cross_timeframe': {
                'timeframes': ['1h', '4h', '1d'],
                'alignment_features': True
            }
        }

    def engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        主要的特征工程入口方法

        Args:
            data: 原始OHLCV数据

        Returns:
            包含所有特征的DataFrame
        """
        self.feature_log = []  # 重置日志
        self.feature_log.append("🔧 Starting enhanced feature engineering...")

        try:
            # 调用完整的特征提取
            features_df = self.extract_all_features(data)

            # 特征选择（如果配置了）
            feature_selection_config = self.config.get('feature_selection', {})
            if feature_selection_config and len(features_df) > 50:  # 确保有足够数据进行特征选择
                try:
                    # 创建目标变量（未来收益率）
                    target = features_df['close'].pct_change().shift(-1).fillna(0)

                    # 执行特征选择
                    method = feature_selection_config.get('method', 'correlation')
                    n_features = feature_selection_config.get('n_features', 20)

                    selected_features = self.select_features(features_df, target, method, n_features)

                    if selected_features:
                        # 保留基础列和选择的特征
                        basic_cols = ['open', 'high', 'low', 'close', 'volume']
                        if 'dt' in features_df.columns:
                            basic_cols.append('dt')

                        final_cols = basic_cols + selected_features
                        features_df = features_df[final_cols]

                        self.feature_log.append(f"✅ Feature selection completed: {len(selected_features)} features selected")

                except Exception as e:
                    self.feature_log.append(f"⚠️ Feature selection failed: {e}, using all features")

            self.feature_log.append(f"✅ Enhanced feature engineering completed: {features_df.shape}")
            return features_df

        except Exception as e:
            self.feature_log.append(f"❌ Enhanced feature engineering failed: {e}")
            # 返回基础特征
            return self._create_basic_features(data)

    def _create_basic_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建基础特征作为回退方案"""
        df = data.copy()

        # 基础价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_ma_5'] = df['close'].rolling(5).mean()
        df['price_ma_20'] = df['close'].rolling(20).mean()

        # 基础技术指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # 清理数据
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)

        return df

    def extract_all_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        提取所有特征
        
        Args:
            data: 原始OHLCV数据
            
        Returns:
            包含所有特征的DataFrame
        """
        features_df = data.copy()
        
        self.feature_log.append(f"Starting feature extraction for {len(data)} data points")
        
        # 基础价格特征
        if self.config['price_features']:
            features_df = self._extract_price_features(features_df)
        
        # 技术指标
        if self.config.get('technical_indicators', True):
            features_df = self._extract_technical_indicators(features_df)

        # 成交量特征
        if self.config.get('volume_features', True):
            features_df = self._extract_volume_features(features_df)

        # 波动率特征
        if self.config.get('volatility_features', True):
            features_df = self._extract_volatility_features(features_df)

        # 市场微观结构特征
        if self.config.get('market_microstructure', True):
            features_df = self._extract_microstructure_features(features_df)

        # 模式识别特征
        if self.config.get('pattern_features', True):
            features_df = self._extract_pattern_features(features_df)
        
        # 清理数据
        features_df = self._clean_features(features_df)
        
        self.feature_log.append(f"Feature extraction completed. Total features: {len(features_df.columns)}")
        
        return features_df
    
    def _extract_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取价格相关特征"""
        df = data.copy()
        config = self.config.get('price_features', {})

        # 如果config是布尔值，转换为字典
        if isinstance(config, bool):
            config = {} if not config else {
                'returns': True,
                'log_returns': True,
                'moving_averages': [5, 10, 20, 50],
                'price_ratios': True,
                'price_momentum': True
            }

        # 收益率特征
        if config.get('returns', True):
            df['returns'] = df['close'].pct_change()
            df['returns_1h'] = df['close'].pct_change(periods=1)
            df['returns_1d'] = df['close'].pct_change(periods=24) if len(df) > 24 else 0
        
        if config.get('log_returns', True):
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # 移动平均线
        if config.get('moving_averages'):
            for period in config['moving_averages']:
                df[f'sma_{period}'] = df['close'].rolling(period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                
                # 价格与移动平均线的比率
                if config.get('price_ratios', True):
                    df[f'price_sma_{period}_ratio'] = df['close'] / df[f'sma_{period}']
                    df[f'price_ema_{period}_ratio'] = df['close'] / df[f'ema_{period}']
        
        # 价格动量
        if config.get('price_momentum', True):
            df['momentum_1'] = df['close'] / df['close'].shift(1) - 1
            df['momentum_3'] = df['close'] / df['close'].shift(3) - 1
            df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
            df['momentum_10'] = df['close'] / df['close'].shift(10) - 1
        
        # 价格位置特征
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['open_close_ratio'] = df['close'] / df['open']
        
        return df
    
    def _extract_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取技术指标特征"""
        df = data.copy()
        config = self.config.get('technical_indicators', {})

        # 如果config是布尔值，转换为字典
        if isinstance(config, bool):
            config = {} if not config else {
                'rsi': True,
                'macd': True,
                'bollinger_bands': True,
                'stochastic': True,
                'williams_r': True,
                'cci': True,
                'adx': True,
                'advanced_indicators': True
            }

        # RSI (多周期)
        if config.get('rsi', True):
            for period in [14, 21, 28]:
                if TALIB_AVAILABLE:
                    df[f'rsi_{period}'] = talib.RSI(df['close'].values, timeperiod=period)
                else:
                    df[f'rsi_{period}'] = self._calculate_rsi(df['close'], period)

        # 高级技术指标
        if config.get('advanced_indicators', True):
            df = self._extract_advanced_indicators(df)
        
        # MACD
        if config.get('macd', True):
            if TALIB_AVAILABLE:
                macd, macd_signal, macd_hist = talib.MACD(df['close'].values)
                df['macd'] = macd
                df['macd_signal'] = macd_signal
                df['macd_histogram'] = macd_hist
            else:
                df = self._calculate_macd(df)
        
        # 布林带
        if config.get('bollinger_bands', True):
            if TALIB_AVAILABLE:
                upper, middle, lower = talib.BBANDS(df['close'].values)
                df['bb_upper'] = upper
                df['bb_middle'] = middle
                df['bb_lower'] = lower
            else:
                df = self._calculate_bollinger_bands(df)
            
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # 随机指标
        if config.get('stochastic', True):
            if TALIB_AVAILABLE:
                slowk, slowd = talib.STOCH(df['high'].values, df['low'].values, df['close'].values)
                df['stoch_k'] = slowk
                df['stoch_d'] = slowd
            else:
                df = self._calculate_stochastic(df)
        
        # Williams %R
        if config.get('williams_r', True):
            if TALIB_AVAILABLE:
                df['williams_r'] = talib.WILLR(df['high'].values, df['low'].values, df['close'].values)
            else:
                df['williams_r'] = self._calculate_williams_r(df)
        
        # CCI
        if config.get('cci', True):
            if TALIB_AVAILABLE:
                df['cci'] = talib.CCI(df['high'].values, df['low'].values, df['close'].values)
            else:
                df['cci'] = self._calculate_cci(df)
        
        return df
    
    def _extract_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取成交量特征"""
        df = data.copy()
        config = self.config.get('volume_features', {})

        # 如果config是布尔值，转换为字典
        if isinstance(config, bool):
            config = {} if not config else {
                'volume_sma': [5, 20],
                'volume_ratios': True,
                'vwap': True,
                'volume_price_trend': True,
                'on_balance_volume': True
            }
        
        # 成交量移动平均
        if config.get('volume_sma'):
            for period in config['volume_sma']:
                df[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
                
                if config.get('volume_ratios', True):
                    df[f'volume_ratio_{period}'] = df['volume'] / df[f'volume_sma_{period}']
        
        # VWAP
        if config.get('vwap', True):
            df['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
            df['price_vwap_ratio'] = df['close'] / df['vwap']
        
        # 成交量价格趋势
        if config.get('volume_price_trend', True):
            df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
        
        # 能量潮
        if config.get('on_balance_volume', True):
            df['obv'] = (df['volume'] * np.sign(df['close'].diff())).cumsum()
        
        # 成交量相关特征
        df['volume_price_correlation'] = df['volume'].rolling(20).corr(df['close'])
        df['volume_volatility'] = df['volume'].rolling(20).std()
        
        return df
    
    def _extract_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取波动率特征"""
        df = data.copy()
        config = self.config.get('volatility_features', {})

        # 如果config是布尔值，转换为字典
        if isinstance(config, bool):
            config = {} if not config else {
                'historical_volatility': [5, 10, 20],
                'true_range': True,
                'average_true_range': True,
                'volatility_ratios': True
            }
        
        # 历史波动率
        if config.get('historical_volatility'):
            for period in config['historical_volatility']:
                df[f'volatility_{period}'] = df['returns'].rolling(period).std() * np.sqrt(252)
        
        # 真实波幅
        if config.get('true_range', True):
            df['true_range'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
        
        # 平均真实波幅
        if config.get('average_true_range', True):
            df['atr'] = df['true_range'].rolling(14).mean()
            df['atr_ratio'] = df['true_range'] / df['atr']
        
        # 波动率比率
        if config.get('volatility_ratios', True):
            df['volatility_ratio_5_20'] = df['volatility_5'] / df['volatility_20']
            df['volatility_ratio_10_20'] = df['volatility_10'] / df['volatility_20']
        
        return df
    
    def _extract_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取市场微观结构特征"""
        df = data.copy()
        
        # 价格效率指标
        df['price_efficiency'] = abs(df['close'] - df['open']) / (df['high'] - df['low'])
        
        # 市场压力指标
        df['buying_pressure'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['selling_pressure'] = (df['high'] - df['close']) / (df['high'] - df['low'])
        
        return df
    
    def _extract_pattern_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取模式识别特征"""
        df = data.copy()
        
        # 支撑阻力位
        df['resistance_level'] = df['high'].rolling(20).max()
        df['support_level'] = df['low'].rolling(20).min()
        df['resistance_distance'] = (df['resistance_level'] - df['close']) / df['close']
        df['support_distance'] = (df['close'] - df['support_level']) / df['close']
        
        return df

    def _extract_advanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取高级技术指标"""
        df = data.copy()

        try:
            # 1. 市场微观结构特征
            df = self._extract_microstructure_features(df)

            # 2. 价格动量和趋势强度
            df = self._extract_momentum_features(df)

            # 3. 波动率聚类特征
            df = self._extract_volatility_clustering_features(df)

            # 4. 支撑阻力位特征
            df = self._extract_support_resistance_features(df)

            # 5. 市场效率指标
            df = self._extract_market_efficiency_features(df)

        except Exception as e:
            self.feature_log.append(f"Advanced indicators extraction failed: {e}")

        return df

    def _extract_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取市场微观结构特征"""
        df = data.copy()

        # 价格跳跃检测
        df['price_jump'] = np.abs(df['close'].pct_change()) > df['close'].pct_change().rolling(20).std() * 2
        df['jump_intensity'] = df['price_jump'].rolling(10).sum()

        # 买卖压力指标
        df['buying_pressure'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['selling_pressure'] = (df['high'] - df['close']) / (df['high'] - df['low'])

        # 价格效率比率
        df['price_efficiency'] = np.abs(df['close'] - df['open']) / (df['high'] - df['low'])

        # 成交量价格趋势 (VPT)
        df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
        df['vpt_ma'] = df['vpt'].rolling(14).mean()
        df['vpt_signal'] = df['vpt'] - df['vpt_ma']

        return df

    def _extract_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取动量特征"""
        df = data.copy()

        # 多周期动量
        for period in [5, 10, 20, 50]:
            df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
            df[f'momentum_ma_{period}'] = df[f'momentum_{period}'].rolling(10).mean()

        # 相对强弱指数的变化率
        if 'rsi_14' in df.columns:
            df['rsi_change'] = df['rsi_14'].diff()
            df['rsi_momentum'] = df['rsi_14'].rolling(5).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])

        # 价格加速度
        df['price_acceleration'] = df['close'].pct_change().diff()
        df['price_velocity'] = df['close'].pct_change().rolling(5).mean()

        return df

    def _extract_volatility_clustering_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取波动率聚类特征"""
        df = data.copy()

        # GARCH效应检测
        returns = df['close'].pct_change().dropna()
        df['volatility_5'] = returns.rolling(5).std()
        df['volatility_20'] = returns.rolling(20).std()
        df['volatility_ratio'] = df['volatility_5'] / df['volatility_20']

        # 波动率持续性
        df['vol_persistence'] = df['volatility_5'].rolling(10).corr(df['volatility_5'].shift(1))

        # 异常波动检测
        vol_threshold = df['volatility_20'].rolling(50).quantile(0.95)
        df['high_volatility_regime'] = (df['volatility_5'] > vol_threshold).astype(int)

        return df

    def _extract_support_resistance_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取支撑阻力位特征"""
        df = data.copy()

        # 简化的支撑阻力位检测
        window = 20
        df['local_max'] = df['high'].rolling(window, center=True).max() == df['high']
        df['local_min'] = df['low'].rolling(window, center=True).min() == df['low']

        # 距离最近支撑阻力位的距离
        df['distance_to_resistance'] = 0.0
        df['distance_to_support'] = 0.0

        for i in range(window, len(df) - window):
            # 寻找最近的阻力位
            recent_resistance = df.loc[max(0, i-50):i, 'high'][df.loc[max(0, i-50):i, 'local_max']].max()
            if not np.isnan(recent_resistance):
                df.iloc[i, df.columns.get_loc('distance_to_resistance')] = (df.iloc[i]['close'] - recent_resistance) / recent_resistance

            # 寻找最近的支撑位
            recent_support = df.loc[max(0, i-50):i, 'low'][df.loc[max(0, i-50):i, 'local_min']].min()
            if not np.isnan(recent_support):
                df.iloc[i, df.columns.get_loc('distance_to_support')] = (df.iloc[i]['close'] - recent_support) / recent_support

        return df

    def _extract_market_efficiency_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取市场效率特征"""
        df = data.copy()

        # 赫斯特指数 (简化版)
        returns = df['close'].pct_change().dropna()
        df['hurst_exponent'] = returns.rolling(50).apply(self._calculate_hurst_exponent)

        # 自相关性
        df['autocorr_1'] = returns.rolling(20).apply(lambda x: x.autocorr(lag=1))
        df['autocorr_5'] = returns.rolling(20).apply(lambda x: x.autocorr(lag=5))

        # 方差比检验
        df['variance_ratio'] = returns.rolling(20).var() / (returns.rolling(10).var() * 2)

        return df

    def _calculate_hurst_exponent(self, ts):
        """计算赫斯特指数 (简化版)"""
        try:
            if len(ts) < 10:
                return 0.5

            lags = range(2, min(20, len(ts)//2))
            tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]

            if len(tau) < 2:
                return 0.5

            poly = np.polyfit(np.log(lags), np.log(tau), 1)
            return poly[0] * 2.0
        except:
            return 0.5

    def select_features(self, features_df: pd.DataFrame, target: pd.Series,
                       method: str = 'correlation', n_features: int = 20) -> List[str]:
        """
        特征选择

        Args:
            features_df: 特征数据
            target: 目标变量
            method: 选择方法
            n_features: 选择的特征数量

        Returns:
            选择的特征列名列表
        """
        try:
            if method == 'correlation':
                # 基于相关性的特征选择
                correlations = {}
                for col in features_df.columns:
                    if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']:
                        corr = abs(features_df[col].corr(target))
                        if not np.isnan(corr):
                            correlations[col] = corr

                # 选择相关性最高的特征
                selected = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:n_features]
                return [col for col, _ in selected]

            elif method == 'variance':
                # 基于方差的特征选择
                variances = {}
                for col in features_df.columns:
                    if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']:
                        var = features_df[col].var()
                        if not np.isnan(var) and var > 0:
                            variances[col] = var

                selected = sorted(variances.items(), key=lambda x: x[1], reverse=True)[:n_features]
                return [col for col, _ in selected]

            elif method == 'mutual_info':
                # 基于互信息的特征选择
                try:
                    from sklearn.feature_selection import mutual_info_regression

                    # 准备数据
                    feature_cols = [col for col in features_df.columns
                                  if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']]
                    X = features_df[feature_cols].fillna(0)
                    y = target.fillna(0)

                    # 计算互信息
                    mi_scores = mutual_info_regression(X, y, random_state=42)
                    mi_dict = dict(zip(feature_cols, mi_scores))

                    # 选择互信息最高的特征
                    selected = sorted(mi_dict.items(), key=lambda x: x[1], reverse=True)[:n_features]
                    return [col for col, _ in selected]
                except ImportError:
                    self.feature_log.append("sklearn not available, falling back to correlation")
                    return self.select_features(features_df, target, 'correlation', n_features)

            elif method == 'ensemble':
                # 集成特征选择方法
                return self._ensemble_feature_selection(features_df, target, n_features)

            elif method == 'stability':
                # 基于稳定性的特征选择
                return self._stability_based_selection(features_df, target, n_features)

            else:
                # 返回所有数值特征
                return [col for col in features_df.columns
                       if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']
                       and features_df[col].dtype in ['float64', 'int64']]

        except Exception as e:
            self.feature_log.append(f"Feature selection failed: {e}")
            return []

    def get_feature_importance(self, features_df: pd.DataFrame, target: pd.Series) -> Dict[str, float]:
        """
        计算特征重要性

        Args:
            features_df: 特征数据
            target: 目标变量

        Returns:
            特征重要性字典
        """
        importance = {}

        for col in features_df.columns:
            if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']:
                try:
                    corr = abs(features_df[col].corr(target))
                    if not np.isnan(corr):
                        importance[col] = corr
                except:
                    importance[col] = 0.0

        return importance

    def _ensemble_feature_selection(self, features_df: pd.DataFrame, target: pd.Series, n_features: int) -> List[str]:
        """
        集成特征选择方法
        结合多种特征选择方法的结果
        """
        try:
            # 获取不同方法的特征排名
            corr_features = self.select_features(features_df, target, 'correlation', n_features * 2)
            var_features = self.select_features(features_df, target, 'variance', n_features * 2)

            # 计算特征得分
            feature_scores = {}
            all_features = set(corr_features + var_features)

            for feature in all_features:
                score = 0
                # 相关性排名得分
                if feature in corr_features:
                    score += (len(corr_features) - corr_features.index(feature)) / len(corr_features)

                # 方差排名得分
                if feature in var_features:
                    score += (len(var_features) - var_features.index(feature)) / len(var_features)

                feature_scores[feature] = score

            # 选择得分最高的特征
            selected = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)[:n_features]
            return [col for col, _ in selected]

        except Exception as e:
            self.feature_log.append(f"Ensemble feature selection failed: {e}")
            return self.select_features(features_df, target, 'correlation', n_features)

    def _stability_based_selection(self, features_df: pd.DataFrame, target: pd.Series, n_features: int) -> List[str]:
        """
        基于稳定性的特征选择
        选择在不同时间窗口下表现稳定的特征
        """
        try:
            if len(features_df) < 100:
                return self.select_features(features_df, target, 'correlation', n_features)

            # 将数据分成多个时间窗口
            window_size = len(features_df) // 5
            feature_stability = {}

            feature_cols = [col for col in features_df.columns
                          if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']]

            for feature in feature_cols:
                correlations = []

                # 计算每个窗口的相关性
                for i in range(5):
                    start_idx = i * window_size
                    end_idx = min((i + 1) * window_size, len(features_df))

                    if end_idx - start_idx > 10:  # 确保窗口足够大
                        window_corr = abs(features_df[feature].iloc[start_idx:end_idx].corr(
                            target.iloc[start_idx:end_idx]))
                        if not np.isnan(window_corr):
                            correlations.append(window_corr)

                # 计算稳定性（相关性的标准差的倒数）
                if len(correlations) >= 3:
                    stability = np.mean(correlations) / (np.std(correlations) + 1e-8)
                    feature_stability[feature] = stability

            # 选择稳定性最高的特征
            if feature_stability:
                selected = sorted(feature_stability.items(), key=lambda x: x[1], reverse=True)[:n_features]
                return [col for col, _ in selected]
            else:
                return self.select_features(features_df, target, 'correlation', n_features)

        except Exception as e:
            self.feature_log.append(f"Stability-based selection failed: {e}")
            return self.select_features(features_df, target, 'correlation', n_features)

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _calculate_macd(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        return df

    def _calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std_dev: int = 2) -> pd.DataFrame:
        """计算布林带"""
        df['bb_middle'] = df['close'].rolling(period).mean()
        bb_std = df['close'].rolling(period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * std_dev)
        df['bb_lower'] = df['bb_middle'] - (bb_std * std_dev)
        return df

    def _calculate_stochastic(self, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
        """计算随机指标"""
        lowest_low = df['low'].rolling(k_period).min()
        highest_high = df['high'].rolling(k_period).max()
        df['stoch_k'] = 100 * (df['close'] - lowest_low) / (highest_high - lowest_low)
        df['stoch_d'] = df['stoch_k'].rolling(d_period).mean()
        return df

    def _calculate_williams_r(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算Williams %R"""
        highest_high = df['high'].rolling(period).max()
        lowest_low = df['low'].rolling(period).min()
        return -100 * (highest_high - df['close']) / (highest_high - lowest_low)

    def _calculate_cci(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """计算CCI指标"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        sma = typical_price.rolling(period).mean()
        mean_deviation = typical_price.rolling(period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        return (typical_price - sma) / (0.015 * mean_deviation)

    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理特征数据"""
        # 替换无限值
        df = df.replace([np.inf, -np.inf], np.nan)

        # 前向填充NaN值
        df = df.fillna(method='ffill')

        # 剩余NaN值用0填充
        df = df.fillna(0)

        return df
