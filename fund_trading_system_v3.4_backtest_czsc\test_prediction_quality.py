#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测质量提升测试脚本
测试增强版特征工程和模型训练的效果
"""

import sys
import os
import traceback
import warnings
import pandas as pd
import numpy as np
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_enhanced_test_data(n_samples=300):
    """创建增强版测试数据，包含更多模式和噪声"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='D')
    
    # 创建多种价格模式
    base_price = 100
    prices = [base_price]
    
    # 添加多种市场模式
    for i in range(1, n_samples):
        # 趋势组件
        if i < n_samples // 3:
            trend = 0.001  # 上涨趋势
        elif i < 2 * n_samples // 3:
            trend = -0.0005  # 下跌趋势
        else:
            trend = 0.0002  # 震荡趋势
        
        # 周期性组件
        cycle = 0.005 * np.sin(2 * np.pi * i / 20)  # 20天周期
        
        # 波动率聚类
        if i % 50 < 10:  # 每50天有10天高波动
            volatility = 0.03
        else:
            volatility = 0.015
        
        # 随机噪声
        noise = np.random.normal(0, volatility)
        
        # 跳跃（偶尔的大幅变动）
        if np.random.random() < 0.02:  # 2%概率的跳跃
            jump = np.random.choice([-0.05, 0.05])
        else:
            jump = 0
        
        # 综合价格变化
        change = trend + cycle + noise + jump
        prices.append(prices[-1] * (1 + change))
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'dt': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(500000, 2000000, n_samples)
    })
    
    return data


def test_basic_vs_enhanced_features():
    """测试基础特征工程 vs 增强版特征工程"""
    print("🔬 测试基础特征工程 vs 增强版特征工程...")
    
    try:
        from backtest.enhanced_feature_engineer import EnhancedFeatureEngineer
        
        # 创建测试数据
        data = create_enhanced_test_data(200)
        print(f"   ✅ 测试数据创建完成: {len(data)} 条记录")
        
        # 基础特征工程配置
        basic_config = {
            'technical_indicators': True,
            'price_features': True,
            'volume_features': True,
            'volatility_features': True
        }
        
        # 增强版特征工程配置
        enhanced_config = {
            'technical_indicators': {
                'rsi': True,
                'macd': True,
                'bollinger_bands': True,
                'advanced_indicators': True
            },
            'price_features': True,
            'volume_features': True,
            'volatility_features': True,
            'cross_timeframe_features': True,
            'feature_selection': {
                'method': 'ensemble',
                'n_features': 30
            }
        }
        
        # 测试基础特征工程
        basic_engineer = EnhancedFeatureEngineer(basic_config)
        basic_features = basic_engineer.engineer_features(data)
        basic_feature_count = len([col for col in basic_features.columns 
                                 if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']])
        
        print(f"   📊 基础特征工程:")
        print(f"      - 特征数量: {basic_feature_count}")
        print(f"      - 数据形状: {basic_features.shape}")
        
        # 测试增强版特征工程
        enhanced_engineer = EnhancedFeatureEngineer(enhanced_config)
        enhanced_features = enhanced_engineer.engineer_features(data)
        enhanced_feature_count = len([col for col in enhanced_features.columns 
                                    if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']])
        
        print(f"   🚀 增强版特征工程:")
        print(f"      - 特征数量: {enhanced_feature_count}")
        print(f"      - 数据形状: {enhanced_features.shape}")
        
        # 特征质量评估
        target = enhanced_features['close'].pct_change().shift(-1).fillna(0)
        
        # 基础特征的相关性
        basic_feature_cols = [col for col in basic_features.columns 
                            if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']]
        basic_correlations = []
        for col in basic_feature_cols:
            corr = abs(basic_features[col].corr(target))
            if not np.isnan(corr):
                basic_correlations.append(corr)
        
        # 增强版特征的相关性
        enhanced_feature_cols = [col for col in enhanced_features.columns 
                               if col not in ['open', 'high', 'low', 'close', 'volume', 'dt']]
        enhanced_correlations = []
        for col in enhanced_feature_cols:
            corr = abs(enhanced_features[col].corr(target))
            if not np.isnan(corr):
                enhanced_correlations.append(corr)
        
        print(f"   📈 特征质量比较:")
        print(f"      - 基础特征平均相关性: {np.mean(basic_correlations):.4f}")
        print(f"      - 增强版特征平均相关性: {np.mean(enhanced_correlations):.4f}")
        print(f"      - 基础特征最大相关性: {np.max(basic_correlations):.4f}")
        print(f"      - 增强版特征最大相关性: {np.max(enhanced_correlations):.4f}")
        
        # 判断改进效果
        improvement = np.mean(enhanced_correlations) / np.mean(basic_correlations) - 1
        print(f"      - 特征质量改进: {improvement:.2%}")
        
        if improvement > 0.1:
            print("   ✅ 增强版特征工程显著提升了特征质量")
            return True
        else:
            print("   ⚠️ 增强版特征工程改进有限")
            return False
        
    except Exception as e:
        print(f"   ❌ 特征工程测试失败: {e}")
        traceback.print_exc()
        return False


def test_enhanced_model_training():
    """测试增强版模型训练"""
    print("\n🤖 测试增强版模型训练...")
    
    try:
        from backtest.enhanced_model_trainer import EnhancedModelTrainer
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 200
        n_features = 10
        
        # 创建有意义的特征和目标
        X = np.random.randn(n_samples, n_features)
        # 创建非线性关系
        y = (X[:, 0] * X[:, 1] + 0.5 * X[:, 2]**2 - 0.3 * X[:, 3] + 
             0.1 * np.sin(X[:, 4]) + np.random.normal(0, 0.1, n_samples))
        
        # 分割数据
        split_idx = int(0.8 * n_samples)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        print(f"   📊 训练数据: {X_train.shape}, 验证数据: {X_val.shape}")
        
        # 配置增强版模型训练器
        trainer_config = {
            'random_forest': {
                'n_estimators': 50,
                'max_depth': 8
            },
            'gradient_boosting': {
                'n_estimators': 50,
                'learning_rate': 0.1
            }
        }
        
        trainer = EnhancedModelTrainer(trainer_config)
        
        # 训练模型
        results = trainer.train_models(X_train, y_train, X_val, y_val)
        
        print(f"   🎯 训练结果:")
        for name, result in results.items():
            perf = result['performance']
            print(f"      - {name}: R2={perf['r2']:.4f}, MSE={perf['mse']:.6f}")
        
        # 获取最佳模型
        best_model = trainer.get_best_model()
        if best_model:
            print(f"   🏆 最佳模型: {best_model.name}")
        
        # 测试集成模型
        ensemble_model = trainer.create_ensemble(results)
        if ensemble_model:
            print(f"   🎭 集成模型创建成功")
            
            # 测试集成模型预测
            ensemble_pred = ensemble_model.predict(X_val)
            ensemble_mse = np.mean((y_val - ensemble_pred)**2)
            ensemble_r2 = 1 - ensemble_mse / np.var(y_val)
            
            print(f"      - 集成模型: R2={ensemble_r2:.4f}, MSE={ensemble_mse:.6f}")
            
            # 比较单模型和集成模型
            best_r2 = max(result['performance']['r2'] for result in results.values())
            if ensemble_r2 > best_r2:
                print("   ✅ 集成模型优于单个模型")
                return True
            else:
                print("   ⚠️ 集成模型未显著改进")
                return True  # 仍然算成功，因为功能正常
        else:
            print("   ⚠️ 集成模型创建失败")
            return len(results) > 0  # 至少有模型训练成功
        
    except Exception as e:
        print(f"   ❌ 模型训练测试失败: {e}")
        traceback.print_exc()
        return False


def test_integrated_ml_backtest():
    """测试集成的ML回测系统"""
    print("\n🎯 测试集成的ML回测系统...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建测试数据
        data = create_enhanced_test_data(150)
        print(f"   ✅ 测试数据: {len(data)} 条记录")
        
        # 配置增强版ML回测
        ml_config = {
            'model_type': 'ensemble',
            'sequence_length': 15,
            'min_train_samples': 80,
            'feature_engineering': {
                'technical_indicators': {
                    'rsi': True,
                    'macd': True,
                    'advanced_indicators': True
                },
                'price_features': True,
                'volume_features': True,
                'volatility_features': True,
                'feature_selection': {
                    'method': 'ensemble',
                    'n_features': 20
                }
            },
            'model_training': {
                'random_forest': {'n_estimators': 30},
                'gradient_boosting': {'n_estimators': 30}
            },
            'signal_generation': {
                'method': 'adaptive',
                'buy_threshold': 0.01,
                'sell_threshold': -0.01,
                'signal_smoothing': True,
                'min_holding_period': 2
            }
        }
        
        # 创建ML回测引擎
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=ml_config
        )
        
        # 运行回测
        results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)
        
        print(f"   📈 回测结果:")
        print(f"      - 总收益率: {results.get('total_return', 0):.4f}")
        print(f"      - 交易次数: {results.get('trade_count', 0)}")
        print(f"      - 胜率: {results.get('win_rate', 0):.2%}")
        print(f"      - 夏普比率: {results.get('sharpe_ratio', 0):.4f}")
        
        # 检查ML指标
        ml_metrics = results.get('ml_metrics', {})
        if ml_metrics:
            print(f"   🤖 ML指标:")
            if 'model_performance' in ml_metrics:
                print(f"      - 模型性能: {ml_metrics['model_performance']}")
            if 'feature_count' in ml_metrics:
                print(f"      - 特征数量: {ml_metrics['feature_count']}")
            if 'prediction_accuracy' in ml_metrics:
                print(f"      - 预测准确性: {ml_metrics['prediction_accuracy']:.4f}")
        
        # 成功标准
        success = (
            results.get('trade_count', 0) > 0 and
            results.get('total_return', 0) != 0 and
            ml_metrics.get('feature_count', 0) > 10
        )
        
        if success:
            print("   ✅ 集成ML回测系统运行成功")
        else:
            print("   ⚠️ 集成ML回测系统需要进一步优化")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 集成ML回测测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🎯 预测质量提升测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("基础 vs 增强版特征工程", test_basic_vs_enhanced_features),
        ("增强版模型训练", test_enhanced_model_training),
        ("集成ML回测系统", test_integrated_ml_backtest),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 预测质量提升测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed >= total * 0.8:
        print("\n🎉 预测质量提升测试基本通过！")
        print("\n💡 改进特性:")
        print("   ✅ 增强版特征工程")
        print("   ✅ 高级技术指标")
        print("   ✅ 智能特征选择")
        print("   ✅ 模型集成框架")
        print("   ✅ 自适应模型选择")
        print("   ✅ 集成ML回测系统")
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")
    
    return passed >= total * 0.8


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
