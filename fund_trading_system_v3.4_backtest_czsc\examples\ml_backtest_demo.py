#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML增强回测演示脚本
展示如何使用机器学习增强的回测系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from backtest.advanced_backtest_system import AdvancedBacktestSystem
from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
from backtest.enhanced_feature_engineer import EnhancedFeatureEngineer
from backtest.ml_model_manager import MLModelManager
from backtest.ml_backtest_analyzer import MLBacktestAnalyzer

# 导入工作的ML配置
from working_ml_config import create_working_ml_engine, get_working_ml_config, generate_performance_report


def generate_sample_data(n_days: int = 1000) -> pd.DataFrame:
    """
    生成示例金融数据
    
    Args:
        n_days: 数据天数
        
    Returns:
        示例OHLCV数据
    """
    print(f"📊 生成 {n_days} 天的示例数据...")
    
    # 设置随机种子以确保可重现性
    np.random.seed(42)
    
    # 生成日期序列
    dates = pd.date_range(start='2020-01-01', periods=n_days, freq='D')
    
    # 生成价格数据（随机游走 + 趋势）
    initial_price = 100.0
    returns = np.random.normal(0.0005, 0.02, n_days)  # 日收益率
    
    # 添加一些趋势和周期性
    trend = np.linspace(0, 0.5, n_days)  # 长期上升趋势
    cycle = 0.1 * np.sin(2 * np.pi * np.arange(n_days) / 252)  # 年度周期
    
    returns += trend / n_days + cycle / n_days
    
    # 计算价格
    prices = [initial_price]
    for i in range(1, n_days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    prices = np.array(prices)
    
    # 生成OHLC数据
    high_low_range = np.random.uniform(0.005, 0.03, n_days)  # 日内波动范围
    
    open_prices = prices * (1 + np.random.uniform(-0.01, 0.01, n_days))
    close_prices = prices
    high_prices = np.maximum(open_prices, close_prices) * (1 + high_low_range)
    low_prices = np.minimum(open_prices, close_prices) * (1 - high_low_range)
    
    # 生成成交量数据
    base_volume = 1000000
    volume = np.random.lognormal(np.log(base_volume), 0.5, n_days)
    
    # 创建DataFrame
    data = pd.DataFrame({
        'dt': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volume.astype(int)
    })
    
    print(f"✅ 数据生成完成，价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
    
    return data


def demo_basic_ml_backtest():
    """演示基础ML回测功能 - 使用工作的配置"""
    print("\n" + "="*80)
    print("🤖 基础ML回测演示 (使用工作配置)")
    print("="*80)

    # 生成示例数据
    data = generate_sample_data(200)  # 减少数据量，提高成功率

    # 使用经过验证的工作配置
    ml_engine = create_working_ml_engine(initial_capital=100000)

    print("🔧 运行ML增强回测...")
    print("💡 使用经过验证的工作配置:")
    print("   - 禁用增强组件（避免复杂性冲突）")
    print("   - 序列长度: 5")
    print("   - 最小训练样本: 20")
    print("   - 阈值: ±0.005")

    results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)

    # 显示预测值统计信息
    if 'prediction_history' in results and results['prediction_history']:
        predictions = [p['prediction'] for p in results['prediction_history']]
        print(f"\n🔍 预测值统计:")
        print(f"预测值范围: {min(predictions):.6f} - {max(predictions):.6f}")
        print(f"预测值均值: {np.mean(predictions):.6f}")
        print(f"预测值标准差: {np.std(predictions):.6f}")

    # 显示ML日志（最后几条）
    if 'ml_log' in results and results['ml_log']:
        print(f"\n📝 ML日志 (最后5条):")
        for log_entry in results['ml_log'][-5:]:
            print(f"  {log_entry}")

    # 显示结果
    print("\n📊 回测结果:")
    print(f"总收益率: {results.get('total_return', 0):.2%}")
    print(f"夏普比率: {results.get('sharpe_ratio', 0):.3f}")
    print(f"最大回撤: {results.get('max_drawdown', 0):.2%}")
    print(f"交易次数: {results.get('trade_count', 0)}")
    print(f"最终价值: {results.get('final_value', 0):,.2f}")

    if 'ml_metrics' in results:
        ml_metrics = results['ml_metrics']
        print(f"\n🤖 ML指标:")
        print(f"预测次数: {ml_metrics.get('total_predictions', 0)}")
        print(f"信号分布: {ml_metrics.get('signal_distribution', {})}")

    # 成功检查
    success = results.get('trade_count', 0) > 0
    if success:
        print(f"\n✅ ML增强回测成功！产生了 {results.get('trade_count', 0)} 笔交易")
        # 生成详细报告
        generate_performance_report(data, results)
    else:
        print(f"\n⚠️ 未产生交易，可能需要调整参数")

    return results


def demo_feature_engineering():
    """演示特征工程功能"""
    print("\n" + "="*80)
    print("🔬 特征工程演示")
    print("="*80)
    
    # 生成示例数据
    data = generate_sample_data(300)
    
    # 创建增强特征工程器
    feature_engineer = EnhancedFeatureEngineer()
    
    print("🔧 提取特征...")
    features_df = feature_engineer.extract_all_features(data)
    
    print(f"\n📊 特征工程结果:")
    print(f"原始特征数: {len(data.columns)}")
    print(f"增强后特征数: {len(features_df.columns)}")
    print(f"新增特征数: {len(features_df.columns) - len(data.columns)}")
    
    # 显示部分特征
    new_features = [col for col in features_df.columns if col not in data.columns]
    print(f"\n🆕 新增特征示例 (前10个):")
    for i, feature in enumerate(new_features[:10]):
        print(f"  {i+1}. {feature}")
    
    # 特征选择演示
    target = features_df['close'].pct_change().shift(-1).fillna(0)  # 未来收益率
    selected_features = feature_engineer.select_features(
        features_df, target, method='correlation', n_features=15
    )
    
    print(f"\n🎯 选择的重要特征 (前15个):")
    for i, feature in enumerate(selected_features):
        print(f"  {i+1}. {feature}")
    
    return features_df, selected_features


def demo_model_comparison():
    """演示模型比较功能"""
    print("\n" + "="*80)
    print("🏆 模型比较演示")
    print("="*80)
    
    # 生成示例数据
    data = generate_sample_data(400)
    
    # 特征工程
    feature_engineer = EnhancedFeatureEngineer()
    features_df = feature_engineer.extract_all_features(data)
    
    # 准备训练数据
    target = features_df['close'].pct_change().shift(-1).fillna(0)
    feature_cols = [col for col in features_df.columns 
                   if col not in ['dt', 'open', 'high', 'low', 'close', 'volume']]
    
    X = features_df[feature_cols].fillna(0).values
    y = target.values
    
    # 创建模型管理器
    model_manager = MLModelManager()
    
    print("🔧 训练多个模型...")
    training_results = model_manager.train_all_models(X, y, feature_cols)
    
    print(f"\n📊 模型训练结果:")
    for model_name, result in training_results.items():
        if 'error' not in result:
            print(f"\n{model_name}:")
            print(f"  MSE: {result.get('mse', 0):.6f}")
            print(f"  MAE: {result.get('mae', 0):.6f}")
            print(f"  R²: {result.get('r2', 0):.6f}")
        else:
            print(f"\n{model_name}: 训练失败 - {result['error']}")
    
    print(f"\n🏆 最佳模型: {model_manager.best_model}")
    
    return model_manager, training_results


def demo_complete_ml_system():
    """演示完整ML系统"""
    print("\n" + "="*80)
    print("🚀 完整ML系统演示")
    print("="*80)
    
    # 生成示例数据
    data = generate_sample_data(600)
    
    # 配置ML增强系统
    ml_config = {
        'ml_enhanced': {
            'enabled': True,
            'model_type': 'ensemble',
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True
            },
            'model_training': {
                'retrain_frequency': 100,
                'validation_split': 0.2,
                'min_train_samples': 200
            },
            'signal_generation': {
                'method': 'quantile',  # 改为分位数方法
                'upper_quantile': 0.7,
                'lower_quantile': 0.3,
                'signal_smoothing': True,
                'smoothing_window': 3
            }
        }
    }
    
    # 创建高级回测系统
    backtest_system = AdvancedBacktestSystem(config=ml_config)
    
    print("🔧 运行ML增强回测...")
    ml_results = backtest_system.run_ml_enhanced_backtest(data, train_model=True)
    
    # 分析结果
    analyzer = MLBacktestAnalyzer()
    analysis = analyzer.analyze_ml_backtest_results(ml_results)
    
    # 生成报告
    report = analyzer.create_performance_report(analysis)
    print("\n" + report)
    
    return ml_results, analysis


def demo_working_ml_system():
    """演示工作的ML系统 - 确保产生交易"""
    print("\n" + "="*80)
    print("🚀 工作的ML系统演示")
    print("="*80)

    # 创建有明显趋势的演示数据
    print("📊 创建有趋势的演示数据...")
    np.random.seed(42)
    n_samples = 80
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='D')

    # 创建有明显趋势的价格数据
    base_price = 100
    prices = [base_price]

    for i in range(1, n_samples):
        # 前1/3上涨，中1/3下跌，后1/3震荡上涨
        if i < n_samples // 3:
            trend = 0.02  # 2%上涨趋势
        elif i < 2 * n_samples // 3:
            trend = -0.015  # 1.5%下跌趋势
        else:
            trend = 0.01  # 1%震荡上涨

        noise = np.random.normal(0, 0.005)
        change = trend + noise
        prices.append(prices[-1] * (1 + change))

    data = pd.DataFrame({
        'dt': dates,
        'open': prices,
        'high': [p * 1.008 for p in prices],
        'low': [p * 0.992 for p in prices],
        'close': prices,
        'volume': [1000000] * n_samples
    })

    print(f"✅ 数据创建完成: {len(data)} 条记录")
    print(f"   价格范围: {min(prices):.2f} - {max(prices):.2f}")
    print(f"   总涨幅: {(max(prices)/min(prices) - 1)*100:.2f}%")

    # 使用工作配置运行回测
    ml_engine = create_working_ml_engine(initial_capital=100000)

    print("\n🔧 运行工作的ML增强回测...")
    results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)

    # 生成详细报告
    generate_performance_report(data, results)

    return results


def demo_traditional_vs_ml_comparison():
    """演示传统回测与ML回测的比较"""
    print("\n" + "="*80)
    print("⚖️ 传统回测 vs ML回测比较")
    print("="*80)

    # 生成示例数据
    data = generate_sample_data(500)

    # 创建高级回测系统
    backtest_system = AdvancedBacktestSystem()

    print("🔧 运行传统回测与ML回测比较...")
    comparison_results = backtest_system.compare_traditional_vs_ml(data)

    # 显示比较结果
    if 'comparison' in comparison_results:
        comp = comparison_results['comparison']

        print("\n📊 性能比较:")
        if 'performance' in comp:
            for metric, values in comp['performance'].items():
                print(f"\n{metric}:")
                print(f"  传统回测: {values['traditional']:.4f}")
                print(f"  ML回测: {values['ml_enhanced']:.4f}")
                print(f"  改进: {values['improvement']:.4f} ({values['improvement_pct']:.2f}%)")

        print("\n🎯 总体评估:")
        if 'overall_assessment' in comp:
            overall = comp['overall_assessment']
            print(f"ML优势: {'是' if overall['ml_advantage'] else '否'}")
            print(f"改进倍数: {overall['improvement_factor']:.2f}")

    return comparison_results


def main():
    """主演示函数"""
    print("🎯 ML增强回测系统演示")
    print("="*80)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    demos = [
        ("基础ML回测", demo_basic_ml_backtest),
        ("工作的ML系统", demo_working_ml_system),  # 新增：确保产生交易的演示
        ("特征工程", demo_feature_engineering),
        ("模型比较", demo_model_comparison),
        ("完整ML系统", demo_complete_ml_system),
        ("传统vs ML比较", demo_traditional_vs_ml_comparison),
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*20} {demo_name} {'='*20}")
        try:
            result = demo_func()
            results[demo_name] = result
            print(f"✅ {demo_name} 完成")
        except Exception as e:
            print(f"❌ {demo_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            results[demo_name] = {'error': str(e)}
    
    print("\n" + "="*80)
    print("📊 演示总结")
    print("="*80)
    
    successful_demos = []
    failed_demos = []

    for name, result in results.items():
        try:
            # 检查是否是错误结果
            if isinstance(result, dict) and 'error' in result:
                failed_demos.append(name)
            else:
                successful_demos.append(name)
        except Exception:
            # 如果检查过程中出错，认为是成功的（可能是DataFrame等复杂对象）
            successful_demos.append(name)
    
    print(f"✅ 成功演示: {len(successful_demos)}/{len(demos)}")
    for demo in successful_demos:
        print(f"  - {demo}")
    
    if failed_demos:
        print(f"\n❌ 失败演示: {len(failed_demos)}")
        for demo in failed_demos:
            print(f"  - {demo}")
    
    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if len(successful_demos) >= len(demos) * 0.8:
        print("\n🎉 ML增强回测系统演示成功！")
        print("\n💡 主要特性:")
        print("  ✅ 自动特征工程")
        print("  ✅ 多模型训练和比较")
        print("  ✅ ML增强信号生成")
        print("  ✅ 详细性能分析")
        print("  ✅ 传统vs ML比较")
    else:
        print("\n⚠️ 部分演示失败，请检查依赖和配置")
    
    return results


if __name__ == "__main__":
    results = main()
