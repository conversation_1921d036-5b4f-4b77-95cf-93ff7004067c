#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级策略管理器
实现复杂的交易策略、动态仓位管理和风险控制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from abc import ABC, abstractmethod
import warnings

warnings.filterwarnings('ignore')


class BaseStrategy(ABC):
    """基础策略接口"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.position = 0.0  # 当前仓位 (-1到1之间)
        self.cash = 0.0
        self.portfolio_value = 0.0
        self.trade_history = []
        self.performance_metrics = {}
        
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame, predictions: np.ndarray = None) -> float:
        """
        生成交易信号
        
        Args:
            data: 市场数据
            predictions: ML预测结果
            
        Returns:
            信号强度 (-1到1之间)
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: float, current_price: float, 
                              portfolio_value: float, volatility: float = None) -> float:
        """
        计算仓位大小
        
        Args:
            signal: 交易信号强度
            current_price: 当前价格
            portfolio_value: 组合价值
            volatility: 波动率
            
        Returns:
            目标仓位大小
        """
        pass


class MomentumStrategy(BaseStrategy):
    """动量策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("Momentum", config)
        self.lookback_period = config.get('lookback_period', 20)
        self.momentum_threshold = config.get('momentum_threshold', 0.02)
        
    def generate_signal(self, data: pd.DataFrame, predictions: np.ndarray = None) -> float:
        """生成动量信号"""
        if len(data) < self.lookback_period:
            return 0.0
        
        # 计算动量
        recent_returns = data['close'].pct_change().tail(self.lookback_period)
        momentum = recent_returns.mean()
        
        # 结合ML预测（如果有）
        if predictions is not None and len(predictions) > 0:
            ml_signal = predictions[-1]
            # 动量和ML预测的加权组合
            signal = 0.6 * momentum + 0.4 * ml_signal
        else:
            signal = momentum
        
        # 应用阈值
        if abs(signal) < self.momentum_threshold:
            return 0.0
        
        return np.clip(signal * 10, -1, 1)  # 放大并限制在[-1,1]
    
    def calculate_position_size(self, signal: float, current_price: float, 
                              portfolio_value: float, volatility: float = None) -> float:
        """计算动量策略仓位"""
        base_size = abs(signal) * 0.5  # 基础仓位不超过50%
        
        # 根据波动率调整
        if volatility is not None:
            vol_adjustment = min(1.0, 0.02 / max(volatility, 0.01))
            base_size *= vol_adjustment
        
        return base_size * np.sign(signal)


class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("MeanReversion", config)
        self.lookback_period = config.get('lookback_period', 20)
        self.deviation_threshold = config.get('deviation_threshold', 2.0)
        
    def generate_signal(self, data: pd.DataFrame, predictions: np.ndarray = None) -> float:
        """生成均值回归信号"""
        if len(data) < self.lookback_period:
            return 0.0
        
        # 计算价格偏离度
        recent_prices = data['close'].tail(self.lookback_period)
        mean_price = recent_prices.mean()
        std_price = recent_prices.std()
        current_price = data['close'].iloc[-1]
        
        if std_price == 0:
            return 0.0
        
        deviation = (current_price - mean_price) / std_price
        
        # 均值回归信号（价格偏离越大，反向信号越强）
        if abs(deviation) > self.deviation_threshold:
            signal = -np.sign(deviation) * min(abs(deviation) / 3, 1.0)
        else:
            signal = 0.0
        
        # 结合ML预测
        if predictions is not None and len(predictions) > 0:
            ml_signal = predictions[-1]
            signal = 0.7 * signal + 0.3 * ml_signal
        
        return signal
    
    def calculate_position_size(self, signal: float, current_price: float, 
                              portfolio_value: float, volatility: float = None) -> float:
        """计算均值回归策略仓位"""
        base_size = abs(signal) * 0.3  # 基础仓位不超过30%
        
        # 根据信号强度调整
        strength_adjustment = min(1.0, abs(signal) * 2)
        base_size *= strength_adjustment
        
        return base_size * np.sign(signal)


class MLEnhancedStrategy(BaseStrategy):
    """ML增强策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("MLEnhanced", config)
        self.confidence_threshold = config.get('confidence_threshold', 0.01)
        self.prediction_window = config.get('prediction_window', 5)
        self.signal_smoothing = config.get('signal_smoothing', True)
        self.signal_history = []
        
    def generate_signal(self, data: pd.DataFrame, predictions: np.ndarray = None) -> float:
        """生成ML增强信号"""
        if predictions is None or len(predictions) == 0:
            return 0.0
        
        # 使用最新预测
        current_prediction = predictions[-1]
        
        # 信号平滑
        if self.signal_smoothing:
            self.signal_history.append(current_prediction)
            if len(self.signal_history) > 5:
                self.signal_history.pop(0)
            
            # 使用移动平均平滑信号
            smoothed_signal = np.mean(self.signal_history)
        else:
            smoothed_signal = current_prediction
        
        # 应用置信度阈值
        if abs(smoothed_signal) < self.confidence_threshold:
            return 0.0
        
        # 标准化信号
        signal = np.clip(smoothed_signal * 20, -1, 1)
        
        return signal
    
    def calculate_position_size(self, signal: float, current_price: float, 
                              portfolio_value: float, volatility: float = None) -> float:
        """计算ML策略仓位"""
        # 基于信号强度的动态仓位
        base_size = abs(signal) * 0.6  # 最大60%仓位
        
        # 根据预测置信度调整
        confidence = abs(signal)
        confidence_adjustment = min(1.0, confidence * 3)
        base_size *= confidence_adjustment
        
        return base_size * np.sign(signal)


class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_position_size = config.get('max_position_size', 0.8)
        self.max_drawdown = config.get('max_drawdown', 0.2)
        self.stop_loss = config.get('stop_loss', 0.05)
        self.take_profit = config.get('take_profit', 0.15)
        self.volatility_limit = config.get('volatility_limit', 0.05)
        
        self.peak_value = 0.0
        self.current_drawdown = 0.0
        
    def check_risk_limits(self, position: float, portfolio_value: float, 
                         current_price: float, entry_price: float = None,
                         volatility: float = None) -> Dict[str, Any]:
        """
        检查风险限制
        
        Returns:
            风险检查结果和建议
        """
        risk_status = {
            'position_ok': True,
            'drawdown_ok': True,
            'volatility_ok': True,
            'stop_loss_triggered': False,
            'take_profit_triggered': False,
            'suggested_action': 'hold',
            'risk_score': 0.0
        }
        
        # 1. 仓位大小检查
        if abs(position) > self.max_position_size:
            risk_status['position_ok'] = False
            risk_status['suggested_action'] = 'reduce_position'
            risk_status['risk_score'] += 0.3
        
        # 2. 回撤检查
        if portfolio_value > self.peak_value:
            self.peak_value = portfolio_value
        
        if self.peak_value > 0:
            self.current_drawdown = (self.peak_value - portfolio_value) / self.peak_value
            
            if self.current_drawdown > self.max_drawdown:
                risk_status['drawdown_ok'] = False
                risk_status['suggested_action'] = 'close_all'
                risk_status['risk_score'] += 0.5
        
        # 3. 止损止盈检查
        if entry_price is not None and position != 0:
            pnl_ratio = (current_price - entry_price) / entry_price * np.sign(position)
            
            if pnl_ratio <= -self.stop_loss:
                risk_status['stop_loss_triggered'] = True
                risk_status['suggested_action'] = 'close_position'
                risk_status['risk_score'] += 0.4
            
            elif pnl_ratio >= self.take_profit:
                risk_status['take_profit_triggered'] = True
                risk_status['suggested_action'] = 'close_position'
        
        # 4. 波动率检查
        if volatility is not None and volatility > self.volatility_limit:
            risk_status['volatility_ok'] = False
            risk_status['risk_score'] += 0.2
        
        return risk_status
    
    def adjust_position_for_risk(self, target_position: float, current_position: float,
                                risk_status: Dict[str, Any]) -> float:
        """根据风险状况调整仓位"""
        if risk_status['suggested_action'] == 'close_all':
            return 0.0
        elif risk_status['suggested_action'] == 'close_position':
            return 0.0
        elif risk_status['suggested_action'] == 'reduce_position':
            return target_position * 0.5
        else:
            # 根据风险评分调整
            risk_adjustment = max(0.3, 1.0 - risk_status['risk_score'])
            return target_position * risk_adjustment


class AdvancedStrategyManager:
    """高级策略管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.strategies = {}
        self.risk_manager = RiskManager(config.get('risk_management', {}))
        self.portfolio_state = {
            'cash': 100000,
            'position': 0.0,
            'portfolio_value': 100000,
            'entry_price': None,
            'trade_count': 0
        }
        self.performance_history = []
        
        # 初始化策略
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """初始化策略"""
        strategy_configs = self.config.get('strategies', {})
        
        if 'momentum' in strategy_configs:
            self.strategies['momentum'] = MomentumStrategy(strategy_configs['momentum'])
        
        if 'mean_reversion' in strategy_configs:
            self.strategies['mean_reversion'] = MeanReversionStrategy(strategy_configs['mean_reversion'])
        
        if 'ml_enhanced' in strategy_configs:
            self.strategies['ml_enhanced'] = MLEnhancedStrategy(strategy_configs['ml_enhanced'])
    
    def generate_trading_decision(self, data: pd.DataFrame, predictions: np.ndarray = None) -> Dict[str, Any]:
        """
        生成交易决策
        
        Args:
            data: 市场数据
            predictions: ML预测结果
            
        Returns:
            交易决策
        """
        if len(data) == 0:
            return {'action': 'hold', 'size': 0, 'confidence': 0}
        
        current_price = data['close'].iloc[-1]
        
        # 计算当前波动率
        if len(data) >= 20:
            returns = data['close'].pct_change().tail(20)
            volatility = returns.std()
        else:
            volatility = 0.02
        
        # 收集所有策略信号
        strategy_signals = {}
        strategy_positions = {}
        
        for name, strategy in self.strategies.items():
            try:
                signal = strategy.generate_signal(data, predictions)
                position_size = strategy.calculate_position_size(
                    signal, current_price, self.portfolio_state['portfolio_value'], volatility
                )
                
                strategy_signals[name] = signal
                strategy_positions[name] = position_size
                
            except Exception as e:
                strategy_signals[name] = 0.0
                strategy_positions[name] = 0.0
        
        # 策略集成（加权平均）
        strategy_weights = self.config.get('strategy_weights', {})
        total_weight = sum(strategy_weights.values()) or len(self.strategies)
        
        weighted_signal = 0.0
        weighted_position = 0.0
        
        for name, signal in strategy_signals.items():
            weight = strategy_weights.get(name, 1.0) / total_weight
            weighted_signal += signal * weight
            weighted_position += strategy_positions[name] * weight
        
        # 风险管理检查
        risk_status = self.risk_manager.check_risk_limits(
            self.portfolio_state['position'],
            self.portfolio_state['portfolio_value'],
            current_price,
            self.portfolio_state['entry_price'],
            volatility
        )
        
        # 根据风险调整仓位
        adjusted_position = self.risk_manager.adjust_position_for_risk(
            weighted_position, self.portfolio_state['position'], risk_status
        )
        
        # 生成交易决策
        position_change = adjusted_position - self.portfolio_state['position']
        
        if abs(position_change) < 0.01:  # 最小交易阈值
            action = 'hold'
            size = 0
        elif position_change > 0:
            action = 'buy'
            size = position_change
        else:
            action = 'sell'
            size = abs(position_change)
        
        return {
            'action': action,
            'size': size,
            'target_position': adjusted_position,
            'confidence': abs(weighted_signal),
            'strategy_signals': strategy_signals,
            'risk_status': risk_status,
            'volatility': volatility
        }
    
    def execute_trade(self, decision: Dict[str, Any], current_price: float) -> Dict[str, Any]:
        """执行交易"""
        if decision['action'] == 'hold':
            return {'executed': False, 'reason': 'hold_signal'}
        
        # 计算交易金额
        portfolio_value = self.portfolio_state['portfolio_value']
        trade_value = decision['size'] * portfolio_value
        
        # 检查资金充足性
        if decision['action'] == 'buy' and trade_value > self.portfolio_state['cash']:
            return {'executed': False, 'reason': 'insufficient_cash'}
        
        # 执行交易
        if decision['action'] == 'buy':
            shares = trade_value / current_price
            self.portfolio_state['position'] += decision['size']
            self.portfolio_state['cash'] -= trade_value
            
            # 更新入场价格
            if self.portfolio_state['entry_price'] is None:
                self.portfolio_state['entry_price'] = current_price
            
        else:  # sell
            shares = trade_value / current_price
            self.portfolio_state['position'] -= decision['size']
            self.portfolio_state['cash'] += trade_value
            
            # 如果完全平仓，重置入场价格
            if abs(self.portfolio_state['position']) < 0.01:
                self.portfolio_state['entry_price'] = None
        
        self.portfolio_state['trade_count'] += 1
        
        return {
            'executed': True,
            'action': decision['action'],
            'size': decision['size'],
            'price': current_price,
            'shares': shares,
            'trade_id': self.portfolio_state['trade_count']
        }
    
    def update_portfolio_value(self, current_price: float):
        """更新组合价值"""
        position_value = abs(self.portfolio_state['position']) * self.portfolio_state['portfolio_value']
        self.portfolio_state['portfolio_value'] = self.portfolio_state['cash'] + position_value
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            'portfolio_value': self.portfolio_state['portfolio_value'],
            'cash': self.portfolio_state['cash'],
            'position': self.portfolio_state['position'],
            'trade_count': self.portfolio_state['trade_count'],
            'current_drawdown': self.risk_manager.current_drawdown
        }
