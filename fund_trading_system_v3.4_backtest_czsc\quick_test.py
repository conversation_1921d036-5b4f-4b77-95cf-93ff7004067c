#!/usr/bin/env python3
"""
快速验证ML修复
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np

def main():
    print("🔧 Quick ML Fix Test")
    print("="*30)
    
    try:
        # 导入模块
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建简单数据
        data = pd.DataFrame({
            'datetime': pd.date_range('2023-01-01', periods=150, freq='D'),
            'open': np.random.randn(150) + 100,
            'high': np.random.randn(150) + 102,
            'low': np.random.randn(150) + 98,
            'close': np.random.randn(150) + 100,
            'volume': np.random.randint(1000, 5000, 150)
        })
        
        print(f"📊 Data: {len(data)} rows")
        
        # 创建引擎
        engine = MLEnhancedBacktestEngine()
        print("✅ Engine created")
        
        # 运行回测
        results = engine.run_ml_backtest(data, train_model=True)
        print("✅ Backtest completed")
        
        # 显示关键结果
        if isinstance(results, dict):
            print(f"Trade Count: {results.get('trade_count', 0)}")
            print(f"Total Return: {results.get('total_return', 0):.4f}")
            print(f"ML Enabled: {getattr(engine, 'ml_enabled', False)}")
            
            # 检查ML指标
            ml_metrics = results.get('ml_metrics', {})
            if 'total_predictions' in ml_metrics:
                print(f"Predictions: {ml_metrics['total_predictions']}")
            
            # 成功标准
            success = (
                getattr(engine, 'ml_enabled', False) and
                results.get('trade_count', 0) > 0
            )
            
            if success:
                print("🎉 SUCCESS: ML Enhanced Backtest Working!")
            else:
                print("⚠️ PARTIAL: Some issues remain")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
