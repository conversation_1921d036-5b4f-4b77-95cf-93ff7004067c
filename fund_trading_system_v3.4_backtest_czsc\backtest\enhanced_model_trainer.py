#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版模型训练器
支持模型集成、自适应选择和高级训练策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings
from abc import ABC, abstractmethod

warnings.filterwarnings('ignore')

# 尝试导入机器学习库
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    from sklearn.svm import SVR
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.model_selection import cross_val_score, TimeSeriesSplit
    from sklearn.preprocessing import StandardScaler, RobustScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False


class BaseModel(ABC):
    """基础模型接口"""
    
    def __init__(self, name: str, **kwargs):
        self.name = name
        self.model = None
        self.scaler = None
        self.is_fitted = False
        self.feature_importance_ = None
        
    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'BaseModel':
        """训练模型"""
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        pass
    
    def get_feature_importance(self) -> Optional[np.ndarray]:
        """获取特征重要性"""
        return self.feature_importance_


class RandomForestModel(BaseModel):
    """随机森林模型"""
    
    def __init__(self, **kwargs):
        super().__init__("RandomForest", **kwargs)
        self.model = RandomForestRegressor(
            n_estimators=kwargs.get('n_estimators', 100),
            max_depth=kwargs.get('max_depth', 10),
            min_samples_split=kwargs.get('min_samples_split', 5),
            min_samples_leaf=kwargs.get('min_samples_leaf', 2),
            random_state=42
        )
        self.scaler = RobustScaler()
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'RandomForestModel':
        X_scaled = self.scaler.fit_transform(X)
        self.model.fit(X_scaled, y)
        self.feature_importance_ = self.model.feature_importances_
        self.is_fitted = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)


class GradientBoostingModel(BaseModel):
    """梯度提升模型"""
    
    def __init__(self, **kwargs):
        super().__init__("GradientBoosting", **kwargs)
        self.model = GradientBoostingRegressor(
            n_estimators=kwargs.get('n_estimators', 100),
            learning_rate=kwargs.get('learning_rate', 0.1),
            max_depth=kwargs.get('max_depth', 6),
            random_state=42
        )
        self.scaler = StandardScaler()
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'GradientBoostingModel':
        X_scaled = self.scaler.fit_transform(X)
        self.model.fit(X_scaled, y)
        self.feature_importance_ = self.model.feature_importances_
        self.is_fitted = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)


class XGBoostModel(BaseModel):
    """XGBoost模型"""
    
    def __init__(self, **kwargs):
        super().__init__("XGBoost", **kwargs)
        if XGBOOST_AVAILABLE:
            self.model = xgb.XGBRegressor(
                n_estimators=kwargs.get('n_estimators', 100),
                learning_rate=kwargs.get('learning_rate', 0.1),
                max_depth=kwargs.get('max_depth', 6),
                random_state=42,
                verbosity=0
            )
        else:
            self.model = None
        self.scaler = StandardScaler()
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'XGBoostModel':
        if not XGBOOST_AVAILABLE or self.model is None:
            raise ImportError("XGBoost not available")
        
        X_scaled = self.scaler.fit_transform(X)
        self.model.fit(X_scaled, y)
        self.feature_importance_ = self.model.feature_importances_
        self.is_fitted = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)


class LightGBMModel(BaseModel):
    """LightGBM模型"""
    
    def __init__(self, **kwargs):
        super().__init__("LightGBM", **kwargs)
        if LIGHTGBM_AVAILABLE:
            self.model = lgb.LGBMRegressor(
                n_estimators=kwargs.get('n_estimators', 100),
                learning_rate=kwargs.get('learning_rate', 0.1),
                max_depth=kwargs.get('max_depth', 6),
                random_state=42,
                verbosity=-1
            )
        else:
            self.model = None
        self.scaler = StandardScaler()
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'LightGBMModel':
        if not LIGHTGBM_AVAILABLE or self.model is None:
            raise ImportError("LightGBM not available")
        
        X_scaled = self.scaler.fit_transform(X)
        self.model.fit(X_scaled, y)
        self.feature_importance_ = self.model.feature_importances_
        self.is_fitted = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)


class EnhancedModelTrainer:
    """增强版模型训练器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.models = {}
        self.ensemble_model = None
        self.best_model = None
        self.model_performances = {}
        self.training_log = []
        
    def create_models(self) -> Dict[str, BaseModel]:
        """创建模型集合"""
        models = {}
        
        # 基础模型
        models['rf'] = RandomForestModel(**self.config.get('random_forest', {}))
        models['gb'] = GradientBoostingModel(**self.config.get('gradient_boosting', {}))
        
        # 高级模型（如果可用）
        if XGBOOST_AVAILABLE:
            models['xgb'] = XGBoostModel(**self.config.get('xgboost', {}))
        
        if LIGHTGBM_AVAILABLE:
            models['lgb'] = LightGBMModel(**self.config.get('lightgbm', {}))
        
        return models
    
    def train_models(self, X_train: np.ndarray, y_train: np.ndarray, 
                    X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """训练所有模型并评估性能"""
        self.models = self.create_models()
        results = {}
        
        for name, model in self.models.items():
            try:
                self.training_log.append(f"Training {name} model...")
                
                # 训练模型
                model.fit(X_train, y_train)
                
                # 验证集预测
                val_pred = model.predict(X_val)
                
                # 计算性能指标
                mse = mean_squared_error(y_val, val_pred)
                mae = mean_absolute_error(y_val, val_pred)
                r2 = r2_score(y_val, val_pred)
                
                performance = {
                    'mse': mse,
                    'mae': mae,
                    'r2': r2,
                    'rmse': np.sqrt(mse)
                }
                
                self.model_performances[name] = performance
                results[name] = {
                    'model': model,
                    'performance': performance,
                    'predictions': val_pred
                }
                
                self.training_log.append(f"  {name} - MSE: {mse:.6f}, MAE: {mae:.6f}, R2: {r2:.6f}")
                
            except Exception as e:
                self.training_log.append(f"  {name} training failed: {e}")
                continue
        
        # 选择最佳模型
        if results:
            self.best_model = self._select_best_model(results)
            self.training_log.append(f"Best model: {self.best_model.name}")
        
        return results
    
    def _select_best_model(self, results: Dict[str, Any]) -> BaseModel:
        """选择最佳模型"""
        # 基于R2分数选择最佳模型
        best_name = max(results.keys(), key=lambda x: results[x]['performance']['r2'])
        return results[best_name]['model']
    
    def create_ensemble(self, results: Dict[str, Any]) -> Optional[BaseModel]:
        """创建集成模型"""
        if len(results) < 2:
            return None
        
        try:
            # 选择表现较好的模型进行集成
            good_models = {name: res for name, res in results.items() 
                          if res['performance']['r2'] > 0}
            
            if len(good_models) < 2:
                return None
            
            # 简单的加权平均集成
            class EnsembleModel(BaseModel):
                def __init__(self, models_dict):
                    super().__init__("Ensemble")
                    self.models_dict = models_dict
                    self.weights = self._calculate_weights()
                    self.is_fitted = True
                
                def _calculate_weights(self):
                    # 基于R2分数计算权重
                    r2_scores = [res['performance']['r2'] for res in self.models_dict.values()]
                    total_r2 = sum(max(0, r2) for r2 in r2_scores)
                    if total_r2 == 0:
                        return {name: 1/len(self.models_dict) for name in self.models_dict}
                    return {name: max(0, res['performance']['r2'])/total_r2 
                           for name, res in self.models_dict.items()}
                
                def fit(self, X, y):
                    return self
                
                def predict(self, X):
                    predictions = []
                    weights = []
                    
                    for name, res in self.models_dict.items():
                        try:
                            pred = res['model'].predict(X)
                            predictions.append(pred)
                            weights.append(self.weights[name])
                        except:
                            continue
                    
                    if not predictions:
                        raise ValueError("No models available for prediction")
                    
                    # 加权平均
                    weighted_pred = np.average(predictions, axis=0, weights=weights)
                    return weighted_pred
            
            ensemble = EnsembleModel(good_models)
            self.ensemble_model = ensemble
            self.training_log.append(f"Created ensemble with {len(good_models)} models")
            
            return ensemble
            
        except Exception as e:
            self.training_log.append(f"Ensemble creation failed: {e}")
            return None
    
    def get_best_model(self) -> Optional[BaseModel]:
        """获取最佳模型"""
        return self.best_model
    
    def get_ensemble_model(self) -> Optional[BaseModel]:
        """获取集成模型"""
        return self.ensemble_model
    
    def get_training_log(self) -> List[str]:
        """获取训练日志"""
        return self.training_log
    
    def get_model_performances(self) -> Dict[str, Dict[str, float]]:
        """获取模型性能"""
        return self.model_performances
