"""
ML增强回测配置模块
提供机器学习回测系统的配置管理
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class MLModelConfig:
    """ML模型配置"""
    model_type: str = 'ensemble'  # lstm, gru, transformer, ensemble, random_forest
    sequence_length: int = 30
    prediction_window: int = 5
    retrain_frequency: int = 100
    min_train_samples: int = 200
    validation_split: float = 0.2
    
    # 深度学习模型参数
    hidden_size: int = 64
    num_layers: int = 2
    dropout: float = 0.2
    learning_rate: float = 0.001
    epochs: int = 50
    batch_size: int = 32
    
    # 传统ML模型参数
    n_estimators: int = 100
    max_depth: Optional[int] = 10
    random_state: int = 42


@dataclass
class FeatureEngineeringConfig:
    """特征工程配置"""
    # 基础特征
    price_features: bool = True
    technical_indicators: bool = True
    volume_features: bool = True
    volatility_features: bool = True
    
    # 高级特征
    market_microstructure: bool = True
    pattern_features: bool = True
    cross_timeframe: bool = False
    
    # 特征选择
    feature_selection_enabled: bool = True
    feature_selection_method: str = 'correlation'  # correlation, variance, mutual_info
    max_features: int = 50
    correlation_threshold: float = 0.8
    variance_threshold: float = 0.01


@dataclass
class SignalGenerationConfig:
    """信号生成配置"""
    method: str = 'threshold'  # threshold, quantile, classification, adaptive
    buy_threshold: float = 0.02  # 降低买入阈值，增加交易机会
    sell_threshold: float = -0.02  # 降低卖出阈值，增加交易机会
    confidence_threshold: float = 0.005  # 降低置信度阈值

    # 自适应阈值配置
    adaptive_threshold: bool = True
    threshold_percentile: float = 0.7  # 使用70%分位数作为动态阈值
    lookback_window: int = 50  # 动态阈值计算窗口

    # 信号过滤
    min_holding_period: int = 3  # 增加最小持仓期，减少频繁交易
    max_position_size: float = 0.2  # 增加最大仓位
    signal_smoothing: bool = True
    smoothing_window: int = 5  # 增加平滑窗口

    # 多重信号确认
    require_confirmation: bool = True
    confirmation_window: int = 2  # 信号确认窗口
    min_signal_strength: float = 0.01  # 最小信号强度


@dataclass
class BacktestConfig:
    """回测配置"""
    initial_capital: float = 100000
    transaction_cost: float = 0.001
    slippage: float = 0.0005
    
    # 风险管理
    max_drawdown_limit: float = 0.2
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.15
    max_daily_loss: float = 0.02
    
    # 回测设置
    train_test_split: float = 0.8
    walk_forward_analysis: bool = True
    walk_forward_window: int = 252  # 一年


@dataclass
class AnalysisConfig:
    """分析配置"""
    # 性能分析
    calculate_sharpe: bool = True
    calculate_sortino: bool = True
    calculate_calmar: bool = True
    calculate_var: bool = True
    
    # ML特定分析
    prediction_accuracy: bool = True
    feature_importance: bool = True
    model_comparison: bool = True
    signal_analysis: bool = True
    
    # 可视化
    create_plots: bool = True
    save_plots: bool = True
    plot_format: str = 'png'  # png, jpg, svg, pdf


class MLBacktestConfig:
    """ML回测系统配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.ml_model = MLModelConfig()
        self.feature_engineering = FeatureEngineeringConfig()
        self.signal_generation = SignalGenerationConfig()
        self.backtest = BacktestConfig()
        self.analysis = AnalysisConfig()
        
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def load_config(self, config_file: str):
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新各个配置组件
            if 'ml_model' in config_data:
                self._update_dataclass(self.ml_model, config_data['ml_model'])
            
            if 'feature_engineering' in config_data:
                self._update_dataclass(self.feature_engineering, config_data['feature_engineering'])
            
            if 'signal_generation' in config_data:
                self._update_dataclass(self.signal_generation, config_data['signal_generation'])
            
            if 'backtest' in config_data:
                self._update_dataclass(self.backtest, config_data['backtest'])
            
            if 'analysis' in config_data:
                self._update_dataclass(self.analysis, config_data['analysis'])
                
        except Exception as e:
            print(f"配置加载失败: {e}")
    
    def save_config(self, config_file: str):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        config_data = {
            'ml_model': asdict(self.ml_model),
            'feature_engineering': asdict(self.feature_engineering),
            'signal_generation': asdict(self.signal_generation),
            'backtest': asdict(self.backtest),
            'analysis': asdict(self.analysis)
        }
        
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def _update_dataclass(self, dataclass_instance, update_dict: Dict[str, Any]):
        """更新dataclass实例"""
        for key, value in update_dict.items():
            if hasattr(dataclass_instance, key):
                setattr(dataclass_instance, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'ml_model': asdict(self.ml_model),
            'feature_engineering': asdict(self.feature_engineering),
            'signal_generation': asdict(self.signal_generation),
            'backtest': asdict(self.backtest),
            'analysis': asdict(self.analysis)
        }
    
    def get_advanced_backtest_config(self) -> Dict[str, Any]:
        """
        获取适用于AdvancedBacktestSystem的配置
        
        Returns:
            配置字典
        """
        return {
            'data_preprocessing': {
                'missing_strategy': 'interpolate',
                'outlier_threshold': 3.0
            },
            'feature_engineering': {
                'correlation_threshold': self.feature_engineering.correlation_threshold,
                'variance_threshold': self.feature_engineering.variance_threshold,
                'selection_method': self.feature_engineering.feature_selection_method,
                'n_features': self.feature_engineering.max_features
            },
            'timeseries_processing': {
                'sequence_length': self.ml_model.sequence_length,
                'test_size': 1 - self.backtest.train_test_split,
                'validation_size': self.ml_model.validation_split,
                'scaler_type': 'standard'
            },
            'network_architecture': {
                'model_type': self.ml_model.model_type,
                'lstm_units': [self.ml_model.hidden_size, self.ml_model.hidden_size // 2],
                'dropout_rate': self.ml_model.dropout,
                'bidirectional': False
            },
            'model_training': {
                'epochs': self.ml_model.epochs,
                'batch_size': self.ml_model.batch_size,
                'early_stopping_patience': 10,
                'learning_rate': self.ml_model.learning_rate
            },
            'backtest_engine': {
                'initial_capital': self.backtest.initial_capital,
                'transaction_cost': self.backtest.transaction_cost,
                'slippage': self.backtest.slippage,
                'signal_method': self.signal_generation.method,
                'signal_threshold': self.signal_generation.buy_threshold
            },
            'risk_management': {
                'max_position_size': self.signal_generation.max_position_size,
                'stop_loss_pct': self.backtest.stop_loss_pct,
                'take_profit_pct': self.backtest.take_profit_pct,
                'max_drawdown_limit': self.backtest.max_drawdown_limit,
                'max_daily_loss': self.backtest.max_daily_loss
            },
            'ml_enhanced': {
                'enabled': True,
                'model_type': self.ml_model.model_type,
                'feature_engineering': asdict(self.feature_engineering),
                'model_training': {
                    'retrain_frequency': self.ml_model.retrain_frequency,
                    'validation_split': self.ml_model.validation_split,
                    'min_train_samples': self.ml_model.min_train_samples
                },
                'signal_generation': asdict(self.signal_generation)
            }
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """
        验证配置有效性
        
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        # 验证ML模型配置
        if self.ml_model.sequence_length < 5:
            warnings.append("序列长度过短，可能影响模型性能")
        
        if self.ml_model.prediction_window > self.ml_model.sequence_length:
            errors.append("预测窗口不能大于序列长度")
        
        if not 0 < self.ml_model.validation_split < 1:
            errors.append("验证集比例必须在0和1之间")
        
        # 验证特征工程配置
        if self.feature_engineering.max_features < 5:
            warnings.append("特征数量过少，可能影响模型性能")
        
        # 验证信号生成配置
        if self.signal_generation.buy_threshold <= self.signal_generation.sell_threshold:
            errors.append("买入阈值必须大于卖出阈值")
        
        # 验证回测配置
        if not 0 < self.backtest.train_test_split < 1:
            errors.append("训练测试分割比例必须在0和1之间")
        
        if self.backtest.transaction_cost < 0:
            errors.append("交易成本不能为负数")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    @classmethod
    def create_default_config(cls, save_path: Optional[str] = None) -> 'MLBacktestConfig':
        """
        创建默认配置
        
        Args:
            save_path: 保存路径
            
        Returns:
            配置实例
        """
        config = cls()
        
        if save_path:
            config.save_config(save_path)
        
        return config
    
    @classmethod
    def create_conservative_config(cls) -> 'MLBacktestConfig':
        """创建保守配置"""
        config = cls()
        
        # 保守的模型配置
        config.ml_model.model_type = 'random_forest'
        config.ml_model.sequence_length = 20
        config.ml_model.min_train_samples = 300
        
        # 保守的信号配置
        config.signal_generation.buy_threshold = 0.7
        config.signal_generation.sell_threshold = 0.3
        config.signal_generation.max_position_size = 0.05
        
        # 保守的风险配置
        config.backtest.stop_loss_pct = 0.03
        config.backtest.max_drawdown_limit = 0.1
        
        return config
    
    @classmethod
    def create_aggressive_config(cls) -> 'MLBacktestConfig':
        """创建激进配置"""
        config = cls()
        
        # 激进的模型配置
        config.ml_model.model_type = 'ensemble'
        config.ml_model.sequence_length = 50
        config.ml_model.retrain_frequency = 50
        
        # 激进的信号配置
        config.signal_generation.buy_threshold = 0.55
        config.signal_generation.sell_threshold = 0.45
        config.signal_generation.max_position_size = 0.2
        
        # 激进的风险配置
        config.backtest.stop_loss_pct = 0.08
        config.backtest.take_profit_pct = 0.25
        
        return config
