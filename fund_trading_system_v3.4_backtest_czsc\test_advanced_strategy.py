#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级策略回测测试脚本
测试增强版回测策略和风险管理功能
"""

import sys
import os
import traceback
import warnings
import pandas as pd
import numpy as np
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_realistic_market_data(n_samples=200):
    """创建更真实的市场数据"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='D')
    
    # 创建具有不同市场状态的价格数据
    base_price = 100
    prices = [base_price]
    
    # 市场状态：0=震荡, 1=上涨, 2=下跌
    market_states = []
    current_state = 0
    state_duration = 0
    
    for i in range(1, n_samples):
        # 状态转换逻辑
        if state_duration > 30:  # 状态持续30天后可能转换
            if np.random.random() < 0.3:  # 30%概率转换
                current_state = np.random.choice([0, 1, 2])
                state_duration = 0
        
        market_states.append(current_state)
        state_duration += 1
        
        # 根据市场状态生成价格
        if current_state == 0:  # 震荡
            trend = np.random.normal(0, 0.001)
            volatility = 0.015
        elif current_state == 1:  # 上涨
            trend = np.random.normal(0.002, 0.001)
            volatility = 0.012
        else:  # 下跌
            trend = np.random.normal(-0.0015, 0.001)
            volatility = 0.020
        
        # 添加噪声和跳跃
        noise = np.random.normal(0, volatility)
        jump = 0.03 * np.random.choice([-1, 0, 1], p=[0.02, 0.96, 0.02])
        
        change = trend + noise + jump
        prices.append(prices[-1] * (1 + change))
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'dt': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.008))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.008))) for p in prices],
        'close': prices,
        'volume': np.random.randint(800000, 1500000, n_samples)
    })
    
    return data


def test_advanced_strategy_manager():
    """测试高级策略管理器"""
    print("🎯 测试高级策略管理器...")
    
    try:
        from backtest.advanced_strategy_manager import AdvancedStrategyManager
        
        # 创建测试数据
        data = create_realistic_market_data(100)
        print(f"   ✅ 测试数据创建完成: {len(data)} 条记录")
        
        # 配置策略管理器
        strategy_config = {
            'strategies': {
                'momentum': {
                    'lookback_period': 15,
                    'momentum_threshold': 0.01
                },
                'mean_reversion': {
                    'lookback_period': 20,
                    'deviation_threshold': 1.5
                },
                'ml_enhanced': {
                    'confidence_threshold': 0.005,
                    'signal_smoothing': True
                }
            },
            'strategy_weights': {
                'momentum': 0.4,
                'mean_reversion': 0.3,
                'ml_enhanced': 0.3
            },
            'risk_management': {
                'max_position_size': 0.6,
                'max_drawdown': 0.15,
                'stop_loss': 0.03,
                'take_profit': 0.10,
                'volatility_limit': 0.04
            }
        }
        
        # 创建策略管理器
        strategy_manager = AdvancedStrategyManager(strategy_config)
        print(f"   ✅ 策略管理器初始化完成")
        print(f"      - 策略数量: {len(strategy_manager.strategies)}")
        
        # 模拟回测过程
        decisions = []
        trades = []
        
        # 生成一些模拟ML预测
        np.random.seed(42)
        predictions = np.random.normal(0, 0.02, len(data))
        
        for i in range(20, len(data)):  # 从第20天开始
            current_data = data.iloc[:i+1]
            current_predictions = predictions[:i+1]
            current_price = data.iloc[i]['close']
            
            # 生成交易决策
            decision = strategy_manager.generate_trading_decision(
                current_data, current_predictions
            )
            decisions.append(decision)
            
            # 执行交易
            if decision['action'] != 'hold':
                trade_result = strategy_manager.execute_trade(decision, current_price)
                if trade_result['executed']:
                    trades.append(trade_result)
            
            # 更新组合价值
            strategy_manager.update_portfolio_value(current_price)
        
        # 获取最终性能
        final_metrics = strategy_manager.get_performance_metrics()
        
        print(f"   📊 策略测试结果:")
        print(f"      - 决策数量: {len(decisions)}")
        print(f"      - 交易数量: {len(trades)}")
        print(f"      - 最终组合价值: {final_metrics['portfolio_value']:.2f}")
        print(f"      - 当前仓位: {final_metrics['position']:.4f}")
        print(f"      - 现金余额: {final_metrics['cash']:.2f}")
        
        # 成功标准
        success = (
            len(decisions) > 0 and
            len(trades) > 0 and
            final_metrics['portfolio_value'] > 0
        )
        
        if success:
            print("   ✅ 高级策略管理器测试成功")
        else:
            print("   ⚠️ 高级策略管理器需要优化")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 策略管理器测试失败: {e}")
        traceback.print_exc()
        return False


def test_risk_management():
    """测试风险管理功能"""
    print("\n🛡️ 测试风险管理功能...")
    
    try:
        from backtest.advanced_strategy_manager import RiskManager
        
        # 配置风险管理器
        risk_config = {
            'max_position_size': 0.5,
            'max_drawdown': 0.1,
            'stop_loss': 0.03,
            'take_profit': 0.08,
            'volatility_limit': 0.03
        }
        
        risk_manager = RiskManager(risk_config)
        print(f"   ✅ 风险管理器初始化完成")
        
        # 测试不同风险场景
        test_scenarios = [
            {
                'name': '正常情况',
                'position': 0.3,
                'portfolio_value': 105000,
                'current_price': 102,
                'entry_price': 100,
                'volatility': 0.02
            },
            {
                'name': '仓位过大',
                'position': 0.8,
                'portfolio_value': 110000,
                'current_price': 105,
                'entry_price': 100,
                'volatility': 0.02
            },
            {
                'name': '触发止损',
                'position': 0.4,
                'portfolio_value': 95000,
                'current_price': 97,
                'entry_price': 100,
                'volatility': 0.02
            },
            {
                'name': '高波动率',
                'position': 0.3,
                'portfolio_value': 103000,
                'current_price': 101,
                'entry_price': 100,
                'volatility': 0.05
            }
        ]
        
        risk_results = []
        
        for scenario in test_scenarios:
            risk_status = risk_manager.check_risk_limits(
                scenario['position'],
                scenario['portfolio_value'],
                scenario['current_price'],
                scenario['entry_price'],
                scenario['volatility']
            )
            
            adjusted_position = risk_manager.adjust_position_for_risk(
                scenario['position'], scenario['position'], risk_status
            )
            
            risk_results.append({
                'scenario': scenario['name'],
                'original_position': scenario['position'],
                'adjusted_position': adjusted_position,
                'risk_score': risk_status['risk_score'],
                'suggested_action': risk_status['suggested_action']
            })
            
            print(f"   📋 {scenario['name']}:")
            print(f"      - 原始仓位: {scenario['position']:.2f}")
            print(f"      - 调整后仓位: {adjusted_position:.2f}")
            print(f"      - 风险评分: {risk_status['risk_score']:.2f}")
            print(f"      - 建议操作: {risk_status['suggested_action']}")
        
        # 检查风险管理是否正常工作
        success = True
        for result in risk_results:
            if result['scenario'] == '仓位过大' and result['adjusted_position'] >= result['original_position']:
                success = False
            if result['scenario'] == '触发止损' and result['suggested_action'] != 'close_position':
                success = False
        
        if success:
            print("   ✅ 风险管理功能测试成功")
        else:
            print("   ⚠️ 风险管理功能需要优化")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 风险管理测试失败: {e}")
        traceback.print_exc()
        return False


def test_integrated_advanced_backtest():
    """测试集成的高级回测系统"""
    print("\n🚀 测试集成的高级回测系统...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建测试数据
        data = create_realistic_market_data(120)
        print(f"   ✅ 测试数据: {len(data)} 条记录")
        
        # 配置高级ML回测
        ml_config = {
            'model_type': 'ensemble',
            'sequence_length': 12,
            'min_train_samples': 60,
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True,
                'feature_selection': {
                    'method': 'correlation',
                    'n_features': 15
                }
            },
            'model_training': {
                'random_forest': {'n_estimators': 20},
                'gradient_boosting': {'n_estimators': 20}
            },
            'strategy_management': {
                'strategies': {
                    'momentum': {
                        'lookback_period': 10,
                        'momentum_threshold': 0.008
                    },
                    'ml_enhanced': {
                        'confidence_threshold': 0.005,
                        'signal_smoothing': True
                    }
                },
                'strategy_weights': {
                    'momentum': 0.6,
                    'ml_enhanced': 0.4
                },
                'risk_management': {
                    'max_position_size': 0.4,
                    'max_drawdown': 0.12,
                    'stop_loss': 0.04,
                    'take_profit': 0.12
                }
            }
        }
        
        # 创建ML回测引擎
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=ml_config
        )
        
        # 运行高级回测
        results = ml_engine.run_advanced_ml_backtest(data, target_col='close', train_model=True)
        
        print(f"   📈 高级回测结果:")
        print(f"      - 总收益率: {results.get('total_return', 0):.4f}")
        print(f"      - 交易次数: {results.get('trade_count', 0)}")
        print(f"      - 胜率: {results.get('win_rate', 0):.2%}")
        print(f"      - 夏普比率: {results.get('sharpe_ratio', 0):.4f}")
        print(f"      - 最大回撤: {results.get('max_drawdown', 0):.4f}")
        print(f"      - 最终价值: {results.get('final_value', 0):.2f}")
        
        # 成功标准
        success = (
            results.get('trade_count', 0) > 0 and
            results.get('total_return', 0) != 0 and
            results.get('final_value', 0) > 0
        )
        
        if success:
            print("   ✅ 集成高级回测系统运行成功")
        else:
            print("   ⚠️ 集成高级回测系统需要进一步优化")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 集成高级回测测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🎯 高级策略回测测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("高级策略管理器", test_advanced_strategy_manager),
        ("风险管理功能", test_risk_management),
        ("集成高级回测系统", test_integrated_advanced_backtest),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 高级策略回测测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed >= total * 0.8:
        print("\n🎉 高级策略回测测试基本通过！")
        print("\n💡 改进特性:")
        print("   ✅ 多策略集成框架")
        print("   ✅ 动态仓位管理")
        print("   ✅ 智能风险控制")
        print("   ✅ 止损止盈机制")
        print("   ✅ 波动率自适应")
        print("   ✅ 高级回测引擎")
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")
    
    return passed >= total * 0.8


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
