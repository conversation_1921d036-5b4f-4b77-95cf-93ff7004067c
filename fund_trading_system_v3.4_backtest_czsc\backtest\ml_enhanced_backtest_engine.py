import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from datetime import datetime, timedelta
import warnings
import logging
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import joblib
import os

warnings.filterwarnings('ignore')

# 尝试导入机器学习框架
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 移除tensorflow依赖，只使用PyTorch
TF_AVAILABLE = False

from .backtest_engine import BacktestEngine


class MLEnhancedBacktestEngine(BacktestEngine):
    """
    机器学习增强的回测引擎
    集成ML预测模型到回测流程中，提供基于预测的交易信号生成
    """
    
    def __init__(self, initial_capital: float = 100000, transaction_cost: float = 0.001,
                 slippage: float = 0.0005, min_holding_period: int = 1,
                 ml_config: Optional[Dict] = None):
        """
        初始化ML增强回测引擎
        
        Args:
            initial_capital: 初始资金
            transaction_cost: 交易成本
            slippage: 滑点
            min_holding_period: 最小持仓期
            ml_config: ML配置字典
        """
        super().__init__(initial_capital, transaction_cost, slippage, min_holding_period)
        
        # ML配置
        self.ml_config = ml_config or self._get_default_ml_config()
        
        # ML组件
        self.models = {}  # 存储训练好的模型
        self.scalers = {}  # 存储特征缩放器
        self.feature_columns = []  # 特征列名
        self.prediction_history = []  # 预测历史
        self.model_performance = {}  # 模型性能指标
        
        # ML回测状态
        self.ml_enabled = False
        self.current_model = None
        self.prediction_window = self.ml_config.get('prediction_window', 5)
        self.retrain_frequency = self.ml_config.get('retrain_frequency', 100)
        self.min_train_samples = self.ml_config.get('min_train_samples', 200)
        
        # 日志设置
        self.logger = logging.getLogger(__name__)
        self.ml_log = []

        # 初始化增强组件
        try:
            from .enhanced_feature_engineer import EnhancedFeatureEngineer
            from .enhanced_model_trainer import EnhancedModelTrainer
            from .advanced_strategy_manager import AdvancedStrategyManager

            self.feature_engineer = EnhancedFeatureEngineer(
                self.ml_config.get('feature_engineering', {})
            )

            self.model_trainer = EnhancedModelTrainer(
                self.ml_config.get('model_training', {})
            )

            self.strategy_manager = AdvancedStrategyManager(
                self.ml_config.get('strategy_management', {})
            )

            self.ml_log.append("✅ Enhanced components initialized successfully")

        except ImportError as e:
            self.feature_engineer = None
            self.model_trainer = None
            self.strategy_manager = None
            self.ml_log.append(f"⚠️ Enhanced components not available: {e}, using basic features")
        
    def _get_default_ml_config(self) -> Dict:
        """获取默认ML配置"""
        return {
            'model_type': 'lstm',  # lstm, gru, transformer, ensemble
            'sequence_length': 30,
            'prediction_window': 5,
            'retrain_frequency': 100,
            'min_train_samples': 200,
            'validation_split': 0.2,
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': True,
                'volatility_features': True
            },
            'model_params': {
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'learning_rate': 0.001,
                'epochs': 50,
                'batch_size': 32
            },
            'signal_generation': {
                'method': 'threshold',  # threshold, quantile, classification
                'buy_threshold': 0.01,   # 降低阈值以适应小的预测值
                'sell_threshold': -0.01, # 负值表示卖出
                'confidence_threshold': 0.005
            }
        }
    
    def load_ml_model(self, model_path: str, model_type: str = 'pytorch') -> bool:
        """
        加载预训练的ML模型
        
        Args:
            model_path: 模型文件路径
            model_type: 模型类型 ('pytorch', 'tensorflow', 'sklearn')
            
        Returns:
            是否加载成功
        """
        try:
            if model_type == 'pytorch' and TORCH_AVAILABLE:
                model = torch.load(model_path, map_location='cpu')
                self.models['main'] = model
                self.ml_enabled = True
                self.current_model = 'main'
                
            elif model_type == 'tensorflow':
                # TensorFlow不再支持，建议使用PyTorch
                self.ml_log.append("TensorFlow models are no longer supported. Please use PyTorch instead.")
                return False
                
            elif model_type == 'sklearn':
                model = joblib.load(model_path)
                self.models['main'] = model
                self.ml_enabled = True
                self.current_model = 'main'
            
            # 尝试加载对应的scaler
            scaler_path = model_path.replace('.pkl', '_scaler.pkl').replace('.pth', '_scaler.pkl')
            if os.path.exists(scaler_path):
                self.scalers['main'] = joblib.load(scaler_path)
            
            self.ml_log.append(f"Successfully loaded {model_type} model from {model_path}")
            return True
            
        except Exception as e:
            self.ml_log.append(f"Failed to load model: {e}")
            return False
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备ML特征

        Args:
            data: 原始数据

        Returns:
            特征数据
        """
        self.ml_log.append(f"🔧 Preparing features from {len(data)} data points")

        # 使用增强版特征工程器（如果可用）
        if self.feature_engineer is not None:
            try:
                features_df = self.feature_engineer.engineer_features(data)

                # 记录特征工程日志
                for log_entry in self.feature_engineer.get_feature_log():
                    self.ml_log.append(f"   {log_entry}")

                self.ml_log.append(f"✅ Enhanced feature engineering completed. Features shape: {features_df.shape}")
                return features_df

            except Exception as e:
                self.ml_log.append(f"⚠️ Enhanced feature engineering failed: {e}, falling back to basic features")

        # 回退到基础特征工程
        features_df = data.copy()
        original_columns = len(features_df.columns)

        # 基础价格特征
        if self.ml_config['feature_engineering']['price_features']:
            features_df['returns'] = features_df['close'].pct_change()
            features_df['log_returns'] = np.log(features_df['close'] / features_df['close'].shift(1))
            features_df['price_ma_5'] = features_df['close'].rolling(5).mean()
            features_df['price_ma_20'] = features_df['close'].rolling(20).mean()
            features_df['price_ratio_ma5'] = features_df['close'] / features_df['price_ma_5']
            features_df['price_ratio_ma20'] = features_df['close'] / features_df['price_ma_20']
        
        # 技术指标特征
        if self.ml_config['feature_engineering']['technical_indicators']:
            # RSI
            delta = features_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features_df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = features_df['close'].ewm(span=12).mean()
            exp2 = features_df['close'].ewm(span=26).mean()
            features_df['macd'] = exp1 - exp2
            features_df['macd_signal'] = features_df['macd'].ewm(span=9).mean()
            features_df['macd_histogram'] = features_df['macd'] - features_df['macd_signal']
        
        # 成交量特征
        if self.ml_config['feature_engineering']['volume_features']:
            features_df['volume_ma_5'] = features_df['volume'].rolling(5).mean()
            features_df['volume_ma_20'] = features_df['volume'].rolling(20).mean()
            features_df['volume_ratio'] = features_df['volume'] / features_df['volume_ma_20']
            features_df['price_volume'] = features_df['close'] * features_df['volume']
        
        # 波动率特征
        if self.ml_config['feature_engineering']['volatility_features']:
            features_df['volatility_5'] = features_df['returns'].rolling(5).std()
            features_df['volatility_20'] = features_df['returns'].rolling(20).std()
            features_df['high_low_ratio'] = features_df['high'] / features_df['low']
            features_df['true_range'] = np.maximum(
                features_df['high'] - features_df['low'],
                np.maximum(
                    abs(features_df['high'] - features_df['close'].shift(1)),
                    abs(features_df['low'] - features_df['close'].shift(1))
                )
            )
            features_df['atr'] = features_df['true_range'].rolling(14).mean()
        
        # 移除无限值和NaN
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(method='ffill').fillna(0)

        # 记录特征信息
        final_columns = len(features_df.columns)
        feature_count = final_columns - original_columns
        self.ml_log.append(f"Feature engineering completed: {original_columns} -> {final_columns} columns (+{feature_count} features)")

        # 保存特征列名
        self.feature_columns = [col for col in features_df.columns if col not in ['datetime', 'date']]
        self.ml_log.append(f"Available features: {len(self.feature_columns)} columns")

        return features_df
    
    def create_sequences(self, features: pd.DataFrame, target_col: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列序列用于ML训练/预测
        
        Args:
            features: 特征数据
            target_col: 目标列名
            
        Returns:
            (X, y) 序列数据
        """
        sequence_length = self.ml_config['sequence_length']
        
        # 选择特征列（排除目标列和时间列）
        feature_cols = [col for col in features.columns
                       if col not in [target_col, 'dt', 'date', 'timestamp', 'datetime']]

        if not self.feature_columns:
            self.feature_columns = feature_cols

        # 确保有足够的特征列
        if len(feature_cols) == 0:
            # 如果没有特征列，使用基本的价格特征
            basic_features = ['open', 'high', 'low', 'close', 'volume']
            feature_cols = [col for col in basic_features if col in features.columns and col != target_col]
            if len(feature_cols) == 0:
                raise ValueError(f"No valid feature columns found. Available columns: {list(features.columns)}")

        self.ml_log.append(f"Using {len(feature_cols)} feature columns: {feature_cols[:5]}{'...' if len(feature_cols) > 5 else ''}")
        
        X, y = [], []
        
        for i in range(sequence_length, len(features)):
            # 特征序列
            X.append(features[feature_cols].iloc[i-sequence_length:i].values)
            
            # 目标值（未来收益率）
            current_price = features[target_col].iloc[i]
            future_price = features[target_col].iloc[min(i + self.prediction_window, len(features) - 1)]
            future_return = (future_price - current_price) / current_price
            y.append(future_return)
        
        return np.array(X), np.array(y)

    def make_prediction(self, features: pd.DataFrame, model_name: str = 'main') -> Optional[float]:
        """
        使用ML模型进行预测

        Args:
            features: 特征数据
            model_name: 模型名称

        Returns:
            预测值
        """
        if not self.ml_enabled or model_name not in self.models:
            return None

        try:
            model = self.models[model_name]
            scaler = self.scalers.get(model_name)

            # 准备输入数据
            X, _ = self.create_sequences(features)
            if len(X) == 0:
                return None

            # 使用最后一个序列进行预测
            last_sequence = X[-1:]
            self.ml_log.append(f"Last sequence shape: {last_sequence.shape}")

            # 展平序列数据以匹配训练时的格式
            last_sequence_flattened = last_sequence.reshape(1, -1)
            self.ml_log.append(f"Flattened sequence shape: {last_sequence_flattened.shape}")

            # 验证特征维度
            if hasattr(self, 'expected_feature_count'):
                expected_features = self.expected_feature_count
                actual_features = last_sequence_flattened.shape[1]
                if actual_features != expected_features:
                    error_msg = f"Feature dimension mismatch: expected {expected_features}, got {actual_features}"
                    self.ml_log.append(f"❌ {error_msg}")
                    return None
                else:
                    self.ml_log.append(f"✅ Feature dimensions match: {actual_features}")

            # 数据缩放
            if scaler:
                last_sequence_scaled = scaler.transform(last_sequence_flattened)
                self.ml_log.append(f"Scaled sequence shape: {last_sequence_scaled.shape}")
            else:
                last_sequence_scaled = last_sequence_flattened
                self.ml_log.append("No scaler available, using raw features")

            # 模型预测
            if TORCH_AVAILABLE and isinstance(model, torch.nn.Module):
                model.eval()
                with torch.no_grad():
                    # PyTorch模型需要原始序列形状
                    input_tensor = torch.FloatTensor(last_sequence)
                    prediction = model(input_tensor).numpy()[0]
                    self.ml_log.append(f"PyTorch prediction: {prediction}")
            elif hasattr(model, 'predict'):
                # sklearn模型使用展平的数据
                prediction = model.predict(last_sequence_scaled)[0]
                self.ml_log.append(f"Sklearn prediction: {prediction}")
            else:
                # 未知模型类型
                self.ml_log.append("Unknown model type for prediction")
                return None

            return float(prediction)

        except Exception as e:
            self.ml_log.append(f"Prediction failed: {e}")
            return None

    def generate_ml_signals(self, features: pd.DataFrame) -> pd.Series:
        """
        基于ML预测生成交易信号

        Args:
            features: 特征数据

        Returns:
            交易信号序列
        """
        self.ml_log.append(f"Generating ML signals from {len(features)} data points")
        signals = pd.Series(0, index=features.index)

        if not self.ml_enabled:
            self.ml_log.append("❌ ML not enabled, returning zero signals")
            return signals

        self.ml_log.append(f"✅ ML enabled, proceeding with signal generation")

        signal_config = self.ml_config['signal_generation']
        method = signal_config['method']

        predictions = []

        # 滚动预测
        sequence_length = self.ml_config['sequence_length']
        self.ml_log.append(f"Making predictions with sequence length {sequence_length}")

        for i in range(sequence_length, len(features)):
            current_features = features.iloc[:i+1]
            prediction = self.make_prediction(current_features)
            predictions.append(prediction if prediction is not None else 0)

        if not predictions:
            self.ml_log.append("❌ No predictions generated")
            return signals

        predictions = np.array(predictions)
        self.ml_log.append(f"✅ Generated {len(predictions)} predictions")
        self.ml_log.append(f"   - Prediction range: [{np.min(predictions):.4f}, {np.max(predictions):.4f}]")
        self.ml_log.append(f"   - Prediction mean: {np.mean(predictions):.4f}")

        # 根据方法生成信号
        self.ml_log.append(f"Generating signals using {method} method")

        if method == 'threshold':
            buy_threshold = signal_config['buy_threshold']
            sell_threshold = signal_config['sell_threshold']
            self.ml_log.append(f"   - Buy threshold: {buy_threshold}")
            self.ml_log.append(f"   - Sell threshold: {sell_threshold}")

            # 直接使用预测值，不进行sigmoid转换
            buy_signals = predictions > buy_threshold
            sell_signals = predictions < sell_threshold

            signals.iloc[sequence_length:sequence_length+len(predictions)][buy_signals] = 1
            signals.iloc[sequence_length:sequence_length+len(predictions)][sell_signals] = -1

            buy_count = np.sum(buy_signals)
            sell_count = np.sum(sell_signals)
            self.ml_log.append(f"   - Generated {buy_count} buy signals, {sell_count} sell signals")

        elif method == 'adaptive':
            # 自适应阈值信号生成
            lookback_window = signal_config.get('lookback_window', 50)
            threshold_percentile = signal_config.get('threshold_percentile', 0.7)

            # 计算动态阈值
            if len(predictions) >= lookback_window:
                recent_predictions = predictions[-lookback_window:]
                dynamic_buy_threshold = np.percentile(recent_predictions, threshold_percentile * 100)
                dynamic_sell_threshold = np.percentile(recent_predictions, (1 - threshold_percentile) * 100)
            else:
                dynamic_buy_threshold = signal_config['buy_threshold']
                dynamic_sell_threshold = signal_config['sell_threshold']

            self.ml_log.append(f"   - Dynamic buy threshold: {dynamic_buy_threshold:.4f}")
            self.ml_log.append(f"   - Dynamic sell threshold: {dynamic_sell_threshold:.4f}")

            buy_signals = predictions > dynamic_buy_threshold
            sell_signals = predictions < dynamic_sell_threshold

            signals.iloc[sequence_length:sequence_length+len(predictions)][buy_signals] = 1
            signals.iloc[sequence_length:sequence_length+len(predictions)][sell_signals] = -1

            buy_count = np.sum(buy_signals)
            sell_count = np.sum(sell_signals)
            self.ml_log.append(f"   - Generated {buy_count} buy signals, {sell_count} sell signals")

        elif method == 'quantile':
            # 基于分位数的信号生成
            upper_quantile = signal_config.get('upper_quantile', 0.8)
            lower_quantile = signal_config.get('lower_quantile', 0.2)

            upper_threshold = np.quantile(predictions, upper_quantile)
            lower_threshold = np.quantile(predictions, lower_quantile)

            self.ml_log.append(f"   - Upper threshold (quantile {upper_quantile}): {upper_threshold:.4f}")
            self.ml_log.append(f"   - Lower threshold (quantile {lower_quantile}): {lower_threshold:.4f}")

            buy_signals = predictions > upper_threshold
            sell_signals = predictions < lower_threshold

            signals.iloc[sequence_length:sequence_length+len(predictions)][buy_signals] = 1
            signals.iloc[sequence_length:sequence_length+len(predictions)][sell_signals] = -1

            buy_count = np.sum(buy_signals)
            sell_count = np.sum(sell_signals)
            self.ml_log.append(f"   - Generated {buy_count} buy signals, {sell_count} sell signals")

        # 应用信号过滤和平滑
        signals = self._apply_signal_filters(signals, signal_config)

        # 记录预测历史
        for i, pred in enumerate(predictions):
            self.prediction_history.append({
                'timestamp': features.index[sequence_length + i],
                'prediction': pred,
                'signal': signals.iloc[sequence_length + i]
            })

        # 最终信号统计
        total_signals = np.sum(signals != 0)
        buy_signals_final = np.sum(signals == 1)
        sell_signals_final = np.sum(signals == -1)
        self.ml_log.append(f"✅ Signal generation completed:")
        self.ml_log.append(f"   - Total signals: {total_signals}")
        self.ml_log.append(f"   - Buy signals: {buy_signals_final}")
        self.ml_log.append(f"   - Sell signals: {sell_signals_final}")

        return signals

    def _apply_signal_filters(self, signals: pd.Series, signal_config: Dict) -> pd.Series:
        """
        应用信号过滤和平滑

        Args:
            signals: 原始信号序列
            signal_config: 信号配置

        Returns:
            过滤后的信号序列
        """
        filtered_signals = signals.copy()

        # 1. 信号平滑
        if signal_config.get('signal_smoothing', True):
            smoothing_window = signal_config.get('smoothing_window', 3)
            self.ml_log.append(f"Applying signal smoothing with window {smoothing_window}")

            # 使用滑动窗口平滑信号
            smoothed = filtered_signals.rolling(window=smoothing_window, center=True).mean()
            # 将平滑后的信号转换回离散信号
            filtered_signals = smoothed.apply(lambda x: 1 if x > 0.5 else (-1 if x < -0.5 else 0))
            filtered_signals = filtered_signals.fillna(0)

        # 2. 最小持仓期过滤
        min_holding_period = signal_config.get('min_holding_period', 1)
        if min_holding_period > 1:
            self.ml_log.append(f"Applying minimum holding period filter: {min_holding_period}")
            filtered_signals = self._apply_min_holding_period(filtered_signals, min_holding_period)

        # 3. 信号确认机制
        if signal_config.get('require_confirmation', False):
            confirmation_window = signal_config.get('confirmation_window', 2)
            min_signal_strength = signal_config.get('min_signal_strength', 0.01)
            self.ml_log.append(f"Applying signal confirmation with window {confirmation_window}")
            filtered_signals = self._apply_signal_confirmation(filtered_signals, confirmation_window)

        # 4. 去除连续相同信号
        filtered_signals = self._remove_consecutive_signals(filtered_signals)

        return filtered_signals

    def _apply_min_holding_period(self, signals: pd.Series, min_period: int) -> pd.Series:
        """应用最小持仓期过滤"""
        filtered = signals.copy()
        current_position = 0
        hold_counter = 0

        for i in range(len(filtered)):
            if current_position == 0:
                # 无持仓时，可以开新仓
                if filtered.iloc[i] != 0:
                    current_position = filtered.iloc[i]
                    hold_counter = 1
            else:
                # 有持仓时，检查是否满足最小持仓期
                if hold_counter < min_period:
                    # 未满足最小持仓期，保持当前仓位
                    filtered.iloc[i] = 0  # 不产生新信号
                    hold_counter += 1
                else:
                    # 满足最小持仓期，可以平仓或换仓
                    if filtered.iloc[i] == -current_position or filtered.iloc[i] == 0:
                        current_position = 0
                        hold_counter = 0
                    elif filtered.iloc[i] != current_position and filtered.iloc[i] != 0:
                        # 换仓
                        current_position = filtered.iloc[i]
                        hold_counter = 1

        return filtered

    def _apply_signal_confirmation(self, signals: pd.Series, confirmation_window: int) -> pd.Series:
        """应用信号确认机制"""
        confirmed_signals = signals.copy()

        for i in range(confirmation_window, len(signals)):
            if signals.iloc[i] != 0:
                # 检查确认窗口内是否有足够的同向信号
                window_signals = signals.iloc[i-confirmation_window:i+1]
                same_direction_count = np.sum(np.sign(window_signals) == np.sign(signals.iloc[i]))

                if same_direction_count < confirmation_window // 2 + 1:
                    confirmed_signals.iloc[i] = 0  # 信号不够强，取消

        return confirmed_signals

    def _remove_consecutive_signals(self, signals: pd.Series) -> pd.Series:
        """去除连续相同信号，只保留第一个"""
        filtered = signals.copy()
        last_signal = 0

        for i in range(len(filtered)):
            current_signal = filtered.iloc[i]
            if current_signal != 0 and current_signal == last_signal:
                filtered.iloc[i] = 0  # 去除连续相同信号
            elif current_signal != 0:
                last_signal = current_signal
            elif current_signal == 0:
                # 遇到0信号时，重置last_signal，允许后续相同方向的信号
                pass

        return filtered

    def run_ml_backtest(self, data: pd.DataFrame, target_col: str = 'close',
                       train_model: bool = True) -> Dict[str, Any]:
        """
        运行ML增强回测

        Args:
            data: 价格数据
            target_col: 目标列名
            train_model: 是否训练模型

        Returns:
            回测结果
        """
        self.ml_log.append(f"🚀 Starting ML backtest with {len(data)} data points")

        # 准备特征
        features = self.prepare_features(data)
        self.ml_log.append(f"📊 Features prepared: {len(features)} rows, {len(features.columns)} columns")

        # 训练模型（如果需要）
        if train_model:
            # 动态调整最小训练样本数
            adjusted_min_samples = min(self.min_train_samples, max(50, len(features) // 3))
            if len(features) >= adjusted_min_samples:
                self.ml_log.append(f"🎯 Training model (sufficient data: {len(features)} >= {adjusted_min_samples})")
                self._train_simple_model(features, target_col)
            else:
                self.ml_log.append(f"⚠️ Insufficient data for training: {len(features)} < {adjusted_min_samples}")
        else:
            self.ml_log.append("⏭️ Skipping model training (train_model=False)")

        # 生成ML信号
        self.ml_log.append("🔮 Generating ML signals...")
        ml_signals = self.generate_ml_signals(features)

        # 运行回测
        self.ml_log.append("📈 Running backtest with ML signals...")
        try:
            # 使用第一个run_backtest方法（简单的data + signals版本）
            # 确保ml_signals是pandas Series
            if isinstance(ml_signals, np.ndarray):
                ml_signals = pd.Series(ml_signals, index=data.index)

            self.ml_log.append(f"ML signals type: {type(ml_signals)}, shape: {ml_signals.shape}")
            self.ml_log.append(f"Data type: {type(data)}, shape: {data.shape}")

            # 实现简单的回测逻辑
            backtest_results = self._run_simple_backtest(data, ml_signals)
            self.ml_log.append("✅ Backtest completed successfully")
        except Exception as e:
            # 如果失败，创建基本的回测结果
            self.ml_log.append(f"❌ Backtest failed: {e}, using simplified results")
            import traceback
            self.ml_log.append(f"Error details: {traceback.format_exc()}")
            backtest_results = {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'trade_count': 0,
                'final_value': self.initial_capital,
                'volatility': 0.0
            }

        # 计算ML特定指标
        ml_metrics = self._calculate_ml_metrics()

        # 合并结果
        enhanced_results = {
            **backtest_results,
            'ml_metrics': ml_metrics,
            'prediction_history': self.prediction_history.copy(),
            'ml_log': self.ml_log.copy(),
            'feature_columns': self.feature_columns.copy(),
            'ml_config': self.ml_config.copy()
        }

        return enhanced_results

    def _run_simple_backtest(self, data: pd.DataFrame, signals: pd.Series) -> Dict[str, float]:
        """
        运行简单的回测逻辑

        Args:
            data: 价格数据
            signals: 交易信号

        Returns:
            回测结果
        """
        try:
            capital = self.initial_capital
            position = 0
            cash = capital
            trades = []

            for i in range(len(data)):
                current_price = data['close'].iloc[i]
                signal = signals.iloc[i]

                if signal == 1 and position == 0:  # 买入信号
                    # 使用95%的现金进行买入，避免全仓
                    available_cash = cash * 0.95
                    # 计算考虑交易成本后的可买入份额
                    effective_price = current_price * (1 + self.transaction_cost)
                    shares = available_cash / effective_price

                    if shares > 0.001:  # 最小交易单位
                        position = shares
                        cost = shares * effective_price
                        cash -= cost
                        trades.append({'type': 'buy', 'price': current_price, 'shares': shares, 'cost': cost})
                        self.ml_log.append(f"   Buy executed: {shares:.4f} shares at {current_price:.4f}, cost: {cost:.2f}")

                elif signal == -1 and position > 0:  # 卖出信号
                    proceeds = position * current_price * (1 - self.transaction_cost)
                    cash += proceeds
                    trades.append({'type': 'sell', 'price': current_price, 'shares': position, 'proceeds': proceeds})
                    self.ml_log.append(f"   Sell executed: {position:.4f} shares at {current_price:.4f}, proceeds: {proceeds:.2f}")
                    position = 0

            # 最终价值
            final_price = data['close'].iloc[-1]
            final_value = cash + position * final_price

            # 计算指标
            total_return = (final_value - capital) / capital
            trade_count = len(trades)

            # 计算胜率
            buy_trades = [t for t in trades if t['type'] == 'buy']
            sell_trades = [t for t in trades if t['type'] == 'sell']

            winning_trades = 0
            if len(buy_trades) > 0 and len(sell_trades) > 0:
                for i in range(min(len(buy_trades), len(sell_trades))):
                    if sell_trades[i]['price'] > buy_trades[i]['price']:
                        winning_trades += 1

            win_rate = winning_trades / max(1, min(len(buy_trades), len(sell_trades)))

            # 计算最大回撤（简化版）
            max_drawdown = 0.0

            # 计算夏普比率（简化版）
            returns = data['close'].pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0

            self.ml_log.append(f"✅ Simple backtest completed: {trade_count} trades, {total_return:.4f} return")

            return {
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'trade_count': trade_count,
                'final_value': final_value,
                'volatility': returns.std()
            }

        except Exception as e:
            self.ml_log.append(f"❌ Simple backtest failed: {e}")
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'trade_count': 0,
                'final_value': self.initial_capital,
                'volatility': 0.0
            }

    def run_advanced_ml_backtest(self, data: pd.DataFrame, target_col: str = 'close',
                                train_model: bool = True) -> Dict[str, Any]:
        """
        运行高级ML回测，使用策略管理器

        Args:
            data: 历史数据
            target_col: 目标列名
            train_model: 是否训练模型

        Returns:
            回测结果
        """
        self.ml_log.append("🚀 Starting advanced ML backtest...")

        try:
            # 1. 特征工程
            features = self.prepare_features(data)
            self.ml_log.append(f"Features prepared: {features.shape}")

            # 2. 训练模型
            if train_model:
                self._train_simple_model(features, target_col)
                if not self.ml_enabled:
                    self.ml_log.append("⚠️ ML training failed, using strategy-only approach")

            # 3. 生成预测
            predictions = []
            if self.ml_enabled:
                sequence_length = self.ml_config.get('sequence_length', 20)
                for i in range(sequence_length, len(features)):
                    try:
                        window_data = features.iloc[:i+1]  # 使用从开始到当前位置的所有数据
                        pred = self.make_prediction(window_data)
                        predictions.append(pred if pred is not None else 0.0)
                    except Exception as e:
                        self.ml_log.append(f"Prediction failed at index {i}: {e}")
                        predictions.append(0.0)
            else:
                predictions = [0.0] * (len(features) - self.ml_config.get('sequence_length', 20))

            predictions = np.array(predictions)
            self.ml_log.append(f"Generated {len(predictions)} predictions")

            # 4. 使用高级策略管理器进行回测
            if self.strategy_manager is not None:
                return self._run_strategy_backtest(data, features, predictions)
            else:
                # 回退到简单回测
                self.ml_log.append("⚠️ Strategy manager not available, using simple backtest")
                signals = self._generate_signals_from_predictions(predictions, features)
                return self._run_simple_backtest(data, signals)

        except Exception as e:
            self.ml_log.append(f"❌ Advanced ML backtest failed: {e}")
            import traceback
            traceback.print_exc()
            return self._get_empty_results()

    def _get_empty_results(self) -> Dict[str, Any]:
        """返回空的回测结果"""
        return {
            'total_return': 0.0,
            'trade_count': 0,
            'win_rate': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'final_value': self.initial_capital,
            'trade_history': [],
            'portfolio_history': [],
            'ml_metrics': {},
            'strategy_type': 'empty'
        }

    def _run_strategy_backtest(self, data: pd.DataFrame, features: pd.DataFrame,
                             predictions: np.ndarray) -> Dict[str, Any]:
        """使用策略管理器运行回测"""
        self.ml_log.append("🎯 Running strategy-based backtest...")

        # 初始化回测状态
        initial_capital = 100000
        portfolio_history = []
        trade_history = []

        sequence_length = self.ml_config.get('sequence_length', 20)
        start_idx = sequence_length

        for i in range(start_idx, len(data)):
            try:
                # 获取当前数据窗口
                current_data = data.iloc[:i+1]
                current_price = data.iloc[i]['close']

                # 获取对应的预测
                pred_idx = i - start_idx
                current_predictions = predictions[:pred_idx+1] if pred_idx >= 0 else np.array([])

                # 生成交易决策
                decision = self.strategy_manager.generate_trading_decision(
                    current_data, current_predictions
                )

                # 执行交易
                trade_result = self.strategy_manager.execute_trade(decision, current_price)

                if trade_result['executed']:
                    trade_history.append({
                        'timestamp': data.index[i] if hasattr(data, 'index') else i,
                        'action': trade_result['action'],
                        'price': current_price,
                        'size': trade_result['size'],
                        'trade_id': trade_result['trade_id']
                    })

                    self.ml_log.append(f"Trade executed: {trade_result['action']} {trade_result['size']:.4f} at {current_price:.4f}")

                # 更新组合价值
                self.strategy_manager.update_portfolio_value(current_price)

                # 记录组合历史
                portfolio_metrics = self.strategy_manager.get_performance_metrics()
                portfolio_history.append({
                    'timestamp': data.index[i] if hasattr(data, 'index') else i,
                    'portfolio_value': portfolio_metrics['portfolio_value'],
                    'position': portfolio_metrics['position'],
                    'cash': portfolio_metrics['cash']
                })

            except Exception as e:
                self.ml_log.append(f"Error at step {i}: {e}")
                continue

        # 计算最终结果
        final_metrics = self.strategy_manager.get_performance_metrics()

        total_return = (final_metrics['portfolio_value'] - initial_capital) / initial_capital
        trade_count = len(trade_history)

        # 计算胜率
        profitable_trades = 0
        if trade_count > 0:
            for j in range(0, len(trade_history), 2):  # 买卖配对
                if j + 1 < len(trade_history):
                    buy_trade = trade_history[j]
                    sell_trade = trade_history[j + 1]
                    if buy_trade['action'] == 'buy' and sell_trade['action'] == 'sell':
                        if sell_trade['price'] > buy_trade['price']:
                            profitable_trades += 1

        win_rate = profitable_trades / max(trade_count // 2, 1) if trade_count > 0 else 0

        # 计算夏普比率
        if len(portfolio_history) > 1:
            returns = []
            for j in range(1, len(portfolio_history)):
                ret = (portfolio_history[j]['portfolio_value'] - portfolio_history[j-1]['portfolio_value']) / portfolio_history[j-1]['portfolio_value']
                returns.append(ret)

            if len(returns) > 0 and np.std(returns) > 0:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0

        # 计算最大回撤
        max_drawdown = final_metrics.get('current_drawdown', 0)

        self.ml_log.append(f"✅ Strategy backtest completed:")
        self.ml_log.append(f"   - Total return: {total_return:.4f}")
        self.ml_log.append(f"   - Trade count: {trade_count}")
        self.ml_log.append(f"   - Win rate: {win_rate:.2%}")
        self.ml_log.append(f"   - Sharpe ratio: {sharpe_ratio:.4f}")

        return {
            'total_return': total_return,
            'trade_count': trade_count,
            'win_rate': win_rate,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_value': final_metrics['portfolio_value'],
            'trade_history': trade_history,
            'portfolio_history': portfolio_history,
            'ml_metrics': self._calculate_ml_metrics(),
            'strategy_type': 'advanced_ml_strategy'
        }

    def print_ml_log(self):
        """打印ML日志用于调试"""
        print("\n" + "="*60)
        print("🔍 ML Enhanced Backtest Debug Log")
        print("="*60)
        for i, log_entry in enumerate(self.ml_log, 1):
            print(f"{i:2d}. {log_entry}")
        print("="*60)

    def _train_simple_model(self, features: pd.DataFrame, target_col: str):
        """
        训练简单的ML模型

        Args:
            features: 特征数据
            target_col: 目标列名
        """
        self.ml_log.append(f"🚀 Starting model training with {len(features)} samples")

        # 使用增强版模型训练器（如果可用）
        if self.model_trainer is not None:
            try:
                return self._train_enhanced_models(features, target_col)
            except Exception as e:
                self.ml_log.append(f"⚠️ Enhanced model training failed: {e}, falling back to basic training")

        # 回退到基础模型训练
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.model_selection import train_test_split

            # 创建序列数据
            X, y = self.create_sequences(features, target_col)
            self.ml_log.append(f"Created sequences: X shape {X.shape}, y shape {y.shape}")

            if len(X) < self.min_train_samples:
                # 降低最小训练样本要求
                adjusted_min_samples = max(50, len(X) // 2)
                self.ml_log.append(f"Adjusting min_train_samples from {self.min_train_samples} to {adjusted_min_samples}")
                if len(X) < adjusted_min_samples:
                    self.ml_log.append(f"Still insufficient data for training: {len(X)} < {adjusted_min_samples}")
                    return

            # 重塑数据用于sklearn
            X_reshaped = X.reshape(X.shape[0], -1)
            self.ml_log.append(f"Reshaped training data: {X.shape} -> {X_reshaped.shape}")

            # 分割训练和验证集
            validation_split = self.ml_config.get('validation_split', 0.2)
            X_train, X_val, y_train, y_val = train_test_split(
                X_reshaped, y, test_size=validation_split,
                shuffle=False
            )
            self.ml_log.append(f"Train/Val split: {X_train.shape} / {X_val.shape}")

            # 训练模型
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )

            # 特征缩放
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)

            # 训练
            model.fit(X_train_scaled, y_train)

            # 验证
            val_predictions = model.predict(X_val_scaled)
            val_mse = np.mean((y_val - val_predictions) ** 2)
            val_mae = np.mean(np.abs(y_val - val_predictions))

            # 保存模型和缩放器
            self.models['main'] = model
            self.scalers['main'] = scaler
            self.ml_enabled = True
            self.current_model = 'main'

            # 保存特征维度信息用于预测时验证
            self.expected_feature_count = X_train.shape[1]
            self.sequence_shape = X.shape[1:]  # (sequence_length, n_features)

            # 记录性能
            self.model_performance['main'] = {
                'validation_mse': val_mse,
                'validation_mae': val_mae,
                'training_samples': len(X_train),
                'validation_samples': len(X_val),
                'feature_count': X_train.shape[1],
                'sequence_shape': self.sequence_shape,
                'expected_flattened_features': self.expected_feature_count
            }

            self.ml_log.append(f"✅ Model trained successfully!")
            self.ml_log.append(f"   - Training samples: {len(X_train)}")
            self.ml_log.append(f"   - Validation samples: {len(X_val)}")
            self.ml_log.append(f"   - Features: {X_train.shape[1]}")
            self.ml_log.append(f"   - Validation MSE: {val_mse:.6f}")
            self.ml_log.append(f"   - Validation MAE: {val_mae:.6f}")
            self.ml_log.append(f"   - ML enabled: {self.ml_enabled}")

        except Exception as e:
            self.ml_log.append(f"Model training failed: {e}")

    def _train_enhanced_models(self, features: pd.DataFrame, target_col: str):
        """
        使用增强版模型训练器训练模型

        Args:
            features: 特征数据
            target_col: 目标列名
        """
        try:
            # 创建序列数据
            X, y = self.create_sequences(features, target_col)
            self.ml_log.append(f"Created sequences: X shape {X.shape}, y shape {y.shape}")

            if len(X) < self.min_train_samples:
                adjusted_min_samples = max(50, len(X) // 2)
                self.ml_log.append(f"Adjusting min_train_samples from {self.min_train_samples} to {adjusted_min_samples}")
                if len(X) < adjusted_min_samples:
                    self.ml_log.append(f"Insufficient training data: {len(X)} < {adjusted_min_samples}")
                    return False

            # 分割训练和验证集
            validation_split = self.ml_config.get('validation_split', 0.2)
            split_idx = int(len(X) * (1 - validation_split))

            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            self.ml_log.append(f"Train set: {X_train.shape}, Validation set: {X_val.shape}")

            # 训练多个模型
            training_results = self.model_trainer.train_models(X_train, y_train, X_val, y_val)

            # 记录训练日志
            for log_entry in self.model_trainer.get_training_log():
                self.ml_log.append(f"   {log_entry}")

            if not training_results:
                self.ml_log.append("No models were successfully trained")
                return False

            # 获取最佳模型
            best_model = self.model_trainer.get_best_model()
            if best_model:
                self.models['main'] = best_model
                self.current_model = 'main'
                self.ml_enabled = True
                self.ml_log.append(f"✅ Best model selected: {best_model.name}")

            # 尝试创建集成模型
            ensemble_model = self.model_trainer.create_ensemble(training_results)
            if ensemble_model:
                self.models['ensemble'] = ensemble_model
                self.ml_log.append("✅ Ensemble model created successfully")

                # 如果集成模型可用，优先使用集成模型
                self.current_model = 'ensemble'

            # 记录模型性能
            self.model_performance = self.model_trainer.get_model_performances()

            return True

        except Exception as e:
            self.ml_log.append(f"Enhanced model training failed: {e}")
            return False

    def _calculate_ml_metrics(self) -> Dict[str, Any]:
        """
        计算ML特定的性能指标

        Returns:
            ML指标字典
        """
        if not self.prediction_history:
            return {}

        predictions = [p['prediction'] for p in self.prediction_history]
        signals = [p['signal'] for p in self.prediction_history]

        metrics = {
            'total_predictions': len(predictions),
            'prediction_mean': np.mean(predictions),
            'prediction_std': np.std(predictions),
            'prediction_min': np.min(predictions),
            'prediction_max': np.max(predictions),
            'signal_distribution': {
                'buy_signals': sum(1 for s in signals if s == 1),
                'sell_signals': sum(1 for s in signals if s == -1),
                'hold_signals': sum(1 for s in signals if s == 0)
            },
            'model_performance': self.model_performance.copy()
        }

        return metrics
