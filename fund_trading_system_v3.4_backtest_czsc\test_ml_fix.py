#!/usr/bin/env python3
"""
ML增强回测系统修复验证测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_simple_test_data(days=100):
    """创建简单测试数据"""
    dates = pd.date_range(start='2023-01-01', periods=days, freq='D')
    
    # 生成简单的价格数据
    np.random.seed(42)
    base_price = 100
    prices = []
    
    for i in range(days):
        # 简单的随机游走
        change = np.random.normal(0, 0.02)
        base_price *= (1 + change)
        prices.append(base_price)
    
    # 创建OHLCV数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        open_price = close * (1 + np.random.normal(0, 0.005))
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000, 5000)
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_ml_fix():
    """测试ML修复"""
    print("🔧 Testing ML Enhanced Backtest Fix")
    print("="*50)
    
    try:
        # 导入模块
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建测试数据
        print("📊 Creating test data...")
        data = create_simple_test_data(100)
        print(f"✅ Test data created: {len(data)} rows")
        
        # 创建ML回测引擎
        print("\n🤖 Initializing ML Enhanced Backtest Engine...")
        ml_engine = MLEnhancedBacktestEngine()
        print("✅ ML engine initialized")
        
        # 运行ML回测
        print("\n🚀 Running ML backtest...")
        results = ml_engine.run_ml_backtest(data, train_model=True)
        
        # 打印调试日志
        print("\n" + "="*50)
        print("🔍 ML Debug Log (last 20 entries):")
        print("="*50)
        for i, log_entry in enumerate(ml_engine.ml_log[-20:], 1):
            print(f"{i:2d}. {log_entry}")
        
        # 显示结果
        print("\n" + "="*50)
        print("📊 ML Backtest Results:")
        print("="*50)
        
        if isinstance(results, dict):
            # 检查关键指标
            trade_count = results.get('trade_count', 0)
            total_return = results.get('total_return', 0)
            ml_metrics = results.get('ml_metrics', {})
            
            print(f"Trade Count: {trade_count}")
            print(f"Total Return: {total_return:.6f}")
            
            if 'model_performance' in ml_metrics:
                model_perf = ml_metrics['model_performance'].get('main', {})
                print(f"Model MSE: {model_perf.get('validation_mse', 'N/A')}")
                print(f"Model MAE: {model_perf.get('validation_mae', 'N/A')}")
                print(f"Expected Features: {model_perf.get('expected_flattened_features', 'N/A')}")
            
            if 'total_predictions' in ml_metrics:
                print(f"Total Predictions: {ml_metrics['total_predictions']}")
                print(f"Prediction Mean: {ml_metrics['prediction_mean']:.6f}")
            
            # 成功标准
            success_criteria = []
            if hasattr(ml_engine, 'ml_enabled') and ml_engine.ml_enabled:
                success_criteria.append("✅ ML enabled")
            else:
                success_criteria.append("❌ ML not enabled")
                
            if trade_count > 0:
                success_criteria.append("✅ Trades generated")
            else:
                success_criteria.append("❌ No trades generated")
                
            if abs(total_return) > 0.001:
                success_criteria.append("✅ Non-zero returns")
            else:
                success_criteria.append("❌ Zero returns")
            
            print("\n" + "="*50)
            print("✅ Success Criteria:")
            print("="*50)
            for criterion in success_criteria:
                print(criterion)
                
            # 总体评估
            success_count = sum(1 for c in success_criteria if c.startswith("✅"))
            total_count = len(success_criteria)
            
            print(f"\n🎯 Overall Success Rate: {success_count}/{total_count}")
            
            if success_count >= 2:
                print("🎉 ML Enhanced Backtest Fix: SUCCESS!")
                return True
            else:
                print("⚠️ ML Enhanced Backtest Fix: PARTIAL SUCCESS")
                return False
        else:
            print(f"❌ Unexpected result type: {type(results)}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ml_fix()
    if success:
        print("\n🎉 ML Fix Test Completed Successfully!")
    else:
        print("\n❌ ML Fix Test Failed!")
