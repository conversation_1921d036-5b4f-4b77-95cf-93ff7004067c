#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML训练修复测试
专门解决ML模型无法启用的问题
"""

import sys
import os
import warnings
import pandas as pd
import numpy as np
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_simple_trending_data(n_samples=60):
    """创建简单的趋势数据，确保ML能学到模式"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='D')
    
    # 创建明显的线性趋势 + 噪声
    base_price = 100
    trend = np.linspace(0, 20, n_samples)  # 20%的总趋势
    noise = np.random.normal(0, 1, n_samples)  # 1%的噪声
    
    prices = base_price + trend + noise
    
    data = pd.DataFrame({
        'dt': dates,
        'open': prices,
        'high': prices * 1.01,
        'low': prices * 0.99,
        'close': prices,
        'volume': [1000000] * n_samples
    })
    
    return data


def test_ml_training_step_by_step():
    """逐步测试ML训练过程"""
    print("🔧 逐步测试ML训练过程...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建简单趋势数据
        data = create_simple_trending_data(60)
        print(f"   ✅ 测试数据: {len(data)} 条记录")
        print(f"   📊 价格趋势: {data['close'].iloc[0]:.2f} -> {data['close'].iloc[-1]:.2f}")
        
        # 最简单的ML配置
        simple_config = {
            'model_type': 'random_forest',
            'sequence_length': 5,  # 很短的序列
            'min_train_samples': 25,  # 很少的训练样本
            'validation_split': 0.2,
            
            'feature_engineering': {
                'technical_indicators': False,  # 关闭复杂特征
                'price_features': True,  # 只用价格特征
                'volume_features': False,
                'volatility_features': False
            }
        }
        
        # 创建ML引擎
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=simple_config
        )
        
        print("   🔧 步骤1: 特征工程...")
        features = ml_engine.prepare_features(data)
        print(f"      特征形状: {features.shape}")
        print(f"      特征列: {list(features.columns)}")
        
        print("   🔧 步骤2: 模型训练...")
        training_success = ml_engine._train_simple_model(features, 'close')
        print(f"      训练结果: {training_success}")
        print(f"      ML启用状态: {ml_engine.ml_enabled}")
        
        if ml_engine.ml_enabled:
            print("   ✅ ML模型训练成功！")
            
            print("   🔧 步骤3: 生成预测...")
            # 测试预测
            sequence_length = simple_config['sequence_length']
            test_window = features.iloc[-sequence_length:]
            
            try:
                prediction = ml_engine._predict_single(test_window)
                print(f"      测试预测值: {prediction:.6f}")
                
                print("   🔧 步骤4: 生成信号...")
                # 生成所有预测
                predictions = []
                for i in range(sequence_length, len(features)):
                    window = features.iloc[i-sequence_length:i]
                    pred = ml_engine._predict_single(window)
                    predictions.append(pred)
                
                predictions = np.array(predictions)
                print(f"      预测数量: {len(predictions)}")
                print(f"      预测范围: [{predictions.min():.6f}, {predictions.max():.6f}]")
                print(f"      预测均值: {predictions.mean():.6f}")
                print(f"      预测标准差: {predictions.std():.6f}")
                
                # 使用极低阈值生成信号
                buy_threshold = 0.0001
                sell_threshold = -0.0001
                
                buy_signals = predictions > buy_threshold
                sell_signals = predictions < sell_threshold
                
                print(f"      买入信号数: {np.sum(buy_signals)}")
                print(f"      卖出信号数: {np.sum(sell_signals)}")
                
                if np.sum(buy_signals) > 0 or np.sum(sell_signals) > 0:
                    print("   ✅ 信号生成成功！")
                    return True
                else:
                    print("   ⚠️ 没有生成信号，阈值可能仍然过高")
                    return False
                
            except Exception as e:
                print(f"      预测失败: {e}")
                return False
        else:
            print("   ❌ ML模型训练失败")
            
            # 打印训练日志
            if hasattr(ml_engine, 'ml_log'):
                print("   🔍 训练日志:")
                for log_entry in ml_engine.ml_log[-10:]:
                    print(f"      {log_entry}")
            
            return False
        
    except Exception as e:
        print(f"   ❌ 逐步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_forced_ml_enable():
    """强制启用ML并测试"""
    print("\n💪 强制启用ML测试...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建数据
        data = create_simple_trending_data(50)
        
        # 简单配置
        config = {
            'model_type': 'random_forest',
            'sequence_length': 5,
            'min_train_samples': 20,
            'validation_split': 0.2,
            'feature_engineering': {
                'price_features': True,
                'technical_indicators': False,
                'volume_features': False,
                'volatility_features': False
            },
            'signal_generation': {
                'method': 'threshold',
                'buy_threshold': 0.0001,
                'sell_threshold': -0.0001
            }
        }
        
        ml_engine = MLEnhancedBacktestEngine(initial_capital=100000, ml_config=config)
        
        # 强制创建一个简单模型
        print("   🔧 强制创建简单模型...")
        
        # 准备特征
        features = ml_engine.prepare_features(data)
        
        # 手动创建模型
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.preprocessing import StandardScaler
            
            # 创建序列数据
            sequence_length = config['sequence_length']
            X, y = [], []
            
            for i in range(sequence_length, len(features)):
                # 使用简单的价格特征
                window_features = []
                for j in range(sequence_length):
                    idx = i - sequence_length + j
                    price_change = features['close'].iloc[idx] / features['close'].iloc[max(0, idx-1)] - 1
                    window_features.append(price_change)
                
                X.append(window_features)
                # 目标：下一期的价格变化
                target = features['close'].iloc[i] / features['close'].iloc[i-1] - 1
                y.append(target)
            
            X = np.array(X)
            y = np.array(y)
            
            print(f"      训练数据形状: X={X.shape}, y={y.shape}")
            
            if len(X) >= 10:  # 确保有足够数据
                # 训练模型
                model = RandomForestRegressor(n_estimators=10, random_state=42)
                scaler = StandardScaler()
                
                X_scaled = scaler.fit_transform(X)
                model.fit(X_scaled, y)
                
                # 强制设置ML状态
                ml_engine.models['main'] = model
                ml_engine.scalers['main'] = scaler
                ml_engine.ml_enabled = True
                ml_engine.current_model = 'main'
                
                print("   ✅ 强制ML启用成功！")
                
                # 测试预测
                test_pred = model.predict(scaler.transform(X[-1:]))
                print(f"      测试预测: {test_pred[0]:.6f}")
                
                # 运行回测
                print("   🚀 运行强制ML回测...")
                results = ml_engine.run_ml_backtest(data, target_col='close', train_model=False)
                
                print(f"   📈 强制ML回测结果:")
                print(f"      - 总收益率: {results.get('total_return', 0):.6f}")
                print(f"      - 交易次数: {results.get('trade_count', 0)}")
                print(f"      - 最终价值: {results.get('final_value', 0):.2f}")
                
                trade_count = results.get('trade_count', 0)
                if trade_count > 0:
                    print("   ✅ 强制ML测试成功！")
                    return True
                else:
                    print("   ⚠️ 强制ML测试仍无交易")
                    return False
            else:
                print("   ❌ 训练数据不足")
                return False
                
        except Exception as e:
            print(f"   ❌ 强制ML创建失败: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ 强制ML测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_direct_signal_generation():
    """直接测试信号生成"""
    print("\n🎯 直接信号生成测试...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建数据
        data = create_simple_trending_data(30)
        
        ml_engine = MLEnhancedBacktestEngine(initial_capital=100000)
        
        # 创建人工预测值（模拟ML预测）
        predictions = np.array([0.001, -0.0005, 0.002, -0.001, 0.0015, 
                               0.0008, -0.0012, 0.0025, -0.0008, 0.0018,
                               0.0005, -0.0015, 0.003, -0.0005, 0.001])
        
        print(f"   人工预测值: {len(predictions)} 个")
        print(f"   预测范围: [{predictions.min():.6f}, {predictions.max():.6f}]")
        
        # 使用极低阈值生成信号
        signal_config = {
            'method': 'threshold',
            'buy_threshold': 0.0005,
            'sell_threshold': -0.0005,
            'signal_smoothing': False,
            'min_holding_period': 1,
            'require_confirmation': False
        }
        
        # 创建特征DataFrame（用于信号生成）
        features = pd.DataFrame({
            'close': data['close'].iloc[:len(predictions)],
            'prediction': predictions
        })
        
        # 直接生成信号
        signals = ml_engine._generate_signals_from_predictions(predictions, features, signal_config)
        
        print(f"   生成信号: {len(signals)} 个")
        print(f"   信号分布: 买入={np.sum(signals == 1)}, 卖出={np.sum(signals == -1)}, 持有={np.sum(signals == 0)}")
        
        if np.sum(signals != 0) > 0:
            print("   ✅ 直接信号生成成功！")
            
            # 运行回测
            backtest_data = data.iloc[:len(signals)]
            results = ml_engine._run_simple_backtest(backtest_data, signals)
            
            print(f"   📈 直接信号回测结果:")
            print(f"      - 收益率: {results['total_return']:.6f}")
            print(f"      - 交易次数: {results['trade_count']}")
            print(f"      - 最终价值: {results['final_value']:.2f}")
            
            return results['trade_count'] > 0
        else:
            print("   ❌ 没有生成任何信号")
            return False
        
    except Exception as e:
        print(f"   ❌ 直接信号生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 ML训练修复测试")
    print("="*80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n目标：修复ML模型无法启用的问题")
    
    tests = [
        ("逐步ML训练测试", test_ml_training_step_by_step),
        ("强制ML启用测试", test_forced_ml_enable),
        ("直接信号生成测试", test_direct_signal_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 ML训练修复测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    print(f"⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed > 0:
        print("\n🎉 找到了可行的解决方案！")
        print("\n💡 关键发现:")
        print("   1. 基础交易逻辑是正常的")
        print("   2. ML模型训练可能存在问题")
        print("   3. 信号生成逻辑需要调整")
        print("   4. 阈值设置需要根据实际预测值调整")
    else:
        print("\n⚠️ 需要进一步调试基础问题")
    
    return passed > 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
