#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试信号生成修复
"""

import sys
import os
import warnings
import pandas as pd
import numpy as np

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_signal_fix():
    """测试信号生成修复"""
    print("🔧 测试信号生成修复...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 生成简单测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 创建价格数据，确保有明显的趋势
        base_price = 100
        prices = []
        for i in range(100):
            # 前50天上涨趋势，后50天下跌趋势
            if i < 50:
                trend = 0.002  # 上涨趋势
            else:
                trend = -0.001  # 下跌趋势
            
            noise = np.random.normal(0, 0.01)
            change = trend + noise
            if i == 0:
                prices.append(base_price)
            else:
                prices.append(prices[-1] * (1 + change))
        
        data = pd.DataFrame({
            'dt': dates,
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': [1000000] * 100
        })
        
        print(f"   ✅ 测试数据: {len(data)} 条记录")
        print(f"   📊 价格范围: {min(prices):.2f} - {max(prices):.2f}")
        
        # 创建ML回测引擎，使用更宽松的阈值
        ml_config = {
            'model_type': 'random_forest',
            'sequence_length': 10,  # 减少序列长度
            'min_train_samples': 50,  # 减少最小训练样本
            'feature_engineering': {
                'technical_indicators': True,
                'price_features': True,
                'volume_features': False,  # 简化特征
                'volatility_features': False
            },
            'signal_generation': {
                'method': 'threshold',
                'buy_threshold': 0.005,  # 更低的阈值
                'sell_threshold': -0.005,
                'signal_smoothing': False,  # 关闭平滑
                'min_holding_period': 1,
                'require_confirmation': False
            }
        }
        
        ml_engine = MLEnhancedBacktestEngine(
            initial_capital=100000,
            ml_config=ml_config
        )
        
        print("   🚀 开始ML回测...")
        
        # 运行回测
        results = ml_engine.run_ml_backtest(data, target_col='close', train_model=True)
        
        print("   ✅ 回测完成")
        print(f"   📈 总收益率: {results.get('total_return', 0):.4f}")
        print(f"   🔄 交易次数: {results.get('trade_count', 0)}")
        print(f"   🎯 胜率: {results.get('win_rate', 0):.2%}")
        print(f"   💰 最终价值: {results.get('final_value', 0):.2f}")
        
        # 检查ML指标
        ml_metrics = results.get('ml_metrics', {})
        if 'signal_distribution' in ml_metrics:
            signal_dist = ml_metrics['signal_distribution']
            print(f"   🚦 信号分布: 买入={signal_dist.get('buy_signals', 0)}, "
                  f"卖出={signal_dist.get('sell_signals', 0)}")
        
        # 检查预测统计
        if 'prediction_mean' in ml_metrics:
            print(f"   🔮 预测统计: 均值={ml_metrics['prediction_mean']:.4f}, "
                  f"标准差={ml_metrics.get('prediction_std', 0):.4f}")
            print(f"   📊 预测范围: [{ml_metrics.get('prediction_min', 0):.4f}, "
                  f"{ml_metrics.get('prediction_max', 0):.4f}]")
        
        # 检查是否成功解决问题
        trade_count = results.get('trade_count', 0)
        if trade_count > 0:
            print(f"\n✅ 成功！生成了 {trade_count} 笔交易")
            return True
        else:
            print(f"\n❌ 仍然没有交易")
            
            # 打印调试信息
            if hasattr(ml_engine, 'ml_log'):
                print("\n🔍 调试信息:")
                for log_entry in ml_engine.ml_log[-10:]:  # 最后10条日志
                    print(f"   {log_entry}")
            
            return False
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_signals():
    """测试手动信号"""
    print("\n🔧 测试手动信号...")
    
    try:
        from backtest.ml_enhanced_backtest_engine import MLEnhancedBacktestEngine
        
        # 创建简单数据
        data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 104, 103, 102, 101],
            'open': [100, 101, 102, 103, 104, 105, 104, 103, 102, 101],
            'high': [101, 102, 103, 104, 105, 106, 105, 104, 103, 102],
            'low': [99, 100, 101, 102, 103, 104, 103, 102, 101, 100],
            'volume': [1000] * 10
        })
        
        # 创建手动信号：第2天买入，第8天卖出
        signals = pd.Series([0, 1, 0, 0, 0, 0, 0, -1, 0, 0])
        
        print(f"   数据: {data['close'].tolist()}")
        print(f"   信号: {signals.tolist()}")
        
        ml_engine = MLEnhancedBacktestEngine(initial_capital=100000)
        
        # 运行简单回测
        backtest_results = ml_engine._run_simple_backtest(data, signals)
        
        print(f"   📈 收益率: {backtest_results['total_return']:.4f}")
        print(f"   🔄 交易次数: {backtest_results['trade_count']}")
        print(f"   💰 最终价值: {backtest_results['final_value']:.2f}")
        
        if backtest_results['trade_count'] > 0:
            print("   ✅ 手动信号测试成功")
            return True
        else:
            print("   ❌ 手动信号测试失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 手动信号测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 信号生成修复测试")
    print("="*50)
    
    # 测试手动信号
    manual_success = test_manual_signals()
    
    # 测试ML信号
    ml_success = test_signal_fix()
    
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    print(f"   手动信号测试: {'✅ 通过' if manual_success else '❌ 失败'}")
    print(f"   ML信号测试: {'✅ 通过' if ml_success else '❌ 失败'}")
    
    if manual_success and ml_success:
        print("\n🎉 所有测试通过！信号生成问题已解决")
    elif manual_success:
        print("\n⚠️ 手动信号正常，ML信号需要进一步调试")
    else:
        print("\n❌ 基础回测逻辑存在问题")
    
    return manual_success and ml_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
